//
//  Connect.m
//  ReactNativeBridgeIos
//
//  Created by PATHOMPHONG CHAROENWICHIANCHAY on 15/10/21.
//

#import "React/RCTViewManager.h"
@interface RCT_EXTERN_MODULE(Connect, RCTViewManager)
RCT_EXTERN_METHOD(goToOcrIdCardVerifyByFace:(NSString *)token isCheckDopa:(BOOL *)checkDopa isExpired:(BOOL *)checkExpiredIdCard  isEnableConfirm:(BOOL *)enableConfirmInfo isPrefill:(NSDictionary *)isPrefill)
RCT_EXTERN_METHOD(goToOcrIdCard:(NSString *)token isCheckDopa:(BOOL *)checkDopa isExpired:(BOOL *)checkExpiredIdCard  isEnableConfirm:(BOOL *)enableConfirmInfo isPrefill:(NSDictionary *)isPrefill)
RCT_EXTERN_METHOD(goToLiveness:(NSString *)token)
RCT_EXTERN_METHOD(goToNdid:(NSString *)token cidInput:(NSString *)cid)
RCT_EXTERN_METHOD(callbackData:(RCTResponseSenderBlock)callback)
@end
