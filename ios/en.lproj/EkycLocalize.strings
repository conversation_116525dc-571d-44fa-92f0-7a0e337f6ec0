/*
 EkycLocalize.strings
 ReactNativeBridgeIos
 
 Created by PATHOMPHONG CHAROENWICHIANCHAY on 19/12/21.
 
 */

"FaceTec_accessibility_cancel_button" = "Cancel";
"FaceTec_accessibility_torch_button" = "Toggle Light";

"FaceTec_action_ok" = "OK";
"FaceTec_action_im_ready" = "I'M READY";
"FaceTec_action_try_again" = "TRY AGAIN";
"FaceTec_action_continue" = "CONTINUE";
"FaceTec_action_take_photo" = "TAKE PHOTO";
"FaceTec_action_retake_photo" = "RETAKE";
"FaceTec_action_accept_photo" = "ACCEPT";
"FaceTec_action_confirm" = "CONFIRM INFO";
"FaceTec_action_scan_nfc" = "SCAN ePASSPORT";
"FaceTec_action_skip_nfc" = "SKIP THIS STEP";

"FaceTec_camera_permission_header" = "Enable Camera";
"FaceTec_camera_permission_message_enroll" = "Please click the button below to enable your selfie camera.";
"FaceTec_camera_permission_message_auth" = "Your camera is disabled. Tap below to edit your settings.";
"FaceTec_camera_permission_enable_camera" = "ENABLE CAMERA";
"FaceTec_camera_permission_launch_settings" = "LAUNCH SETTINGS";

"FaceTec_feedback_center_face" = "Center Your Face";
"FaceTec_feedback_face_not_found" = "Frame Your Face";
"FaceTec_feedback_face_not_looking_straight_ahead" = "Look Straight Ahead";
"FaceTec_feedback_face_not_upright" = "Hold Your Head Straight";
"FaceTec_feedback_hold_steady" = "Hold Steady";
"FaceTec_feedback_move_phone_away" = "Move Away";
"FaceTec_feedback_move_phone_closer" = "Move Closer";
"FaceTec_feedback_move_phone_to_eye_level" = "Move Camera To Eye Level";
"FaceTec_feedback_use_even_lighting" = "Light Face More Evenly";

"FaceTec_idscan_type_selection_header" = "Prepare to Scan\nYour ID Document";
"FaceTec_idscan_capture_tap_to_focus_message" = "Tap Screen To Focus";
"FaceTec_idscan_capture_hold_steady_message" = "Please Hold Steady";
"FaceTec_idscan_capture_id_front_instruction_message" = "Show Front of ID";
"FaceTec_idscan_capture_id_back_instruction_message" = "Show Back of ID";
"FaceTec_idscan_feedback_flip_id_to_back_message" = "Show Back of ID";
"FaceTec_idscan_review_id_front_instruction_message" = "Confirm Photo is Clear & Legible";
"FaceTec_idscan_review_id_back_instruction_message" = "Confirm Photo is Clear & Legible";
"FaceTec_idscan_ocr_confirmation_main_header" = "Review & Confirm";
"FaceTec_idscan_nfc_status_disabled_message" = "Please Enable NFC\nIn Your Device Settings\nTo Continue";
"FaceTec_idscan_nfc_status_ready_message" = "Get Ready to Scan\nYour ePassport Chip";
"FaceTec_idscan_nfc_status_starting_message" = "Hold Phone to Back\nCover of ePassport\nto Scan NFC Chip";
"FaceTec_idscan_nfc_status_scanning_message" = "Hold Steady,\nScanning NFC Chip";
"FaceTec_idscan_nfc_status_weak_connection_message" = "Let's Try\nAnother Scan";
"FaceTec_idscan_nfc_status_finished_with_success_message" = "Document Scan\nComplete";
"FaceTec_idscan_nfc_status_finished_with_error_message" = "ePassport Chip\nUnreadable";
"FaceTec_idscan_nfc_status_skipped_message" = "NFC Scan\nSkipped";

"FaceTec_instructions_header_ready_1" = "Get Ready For";
"FaceTec_instructions_header_ready_2" = "Your Video Selfie";
"FaceTec_instructions_message_ready_1" = "Frame Your Face in the Oval,";
"FaceTec_instructions_message_ready_2" = "Press I’m Ready & Move Closer";

"FaceTec_presession_frame_your_face" = "Frame Your Face In The Oval";
"FaceTec_presession_position_face_straight_in_oval" = "Look Straight Ahead";
"FaceTec_presession_hold_steady_3" = "Hold Steady For: 3";
"FaceTec_presession_hold_steady_2" = "Hold Steady For: 2";
"FaceTec_presession_hold_steady_1" = "Hold Steady For: 1";
"FaceTec_presession_eyes_straight_ahead" = "Look Straight Ahead";
"FaceTec_presession_remove_dark_glasses" = "Remove Dark Glasses";
"FaceTec_presession_neutral_expression" = "Neutral Expression, No Smiling";
"FaceTec_presession_conditions_too_bright" = "Conditions Too Bright";
"FaceTec_presession_brighten_your_environment" = "Brighten Your Environment";

"FaceTec_result_facescan_upload_message" = "Uploading\nEncrypted\n3D FaceScan";
"FaceTec_result_success_message" = "Success";
"FaceTec_result_idscan_upload_message" = "Uploading\nEncrypted\nID Scan";
"FaceTec_result_nfc_upload_message" = "Uploading\nEncrypted\nNFC Details";
"FaceTec_result_idscan_unsuccess_message" = "ID Photo\nDid Not Match\nUser's Face";
"FaceTec_result_idscan_success_front_side_message" = "Front of ID\nScanned";
"FaceTec_result_idscan_success_front_side_back_next_message" = "Front of ID\nScanned";
"FaceTec_initializing_camera" = "Securing Camera Feed";
"FaceTec_result_idscan_success_back_side_message" = "Back of ID Captured";
"FaceTec_result_idscan_success_user_confirmation_message" = "ID Verification\nComplete";
"FaceTec_result_idscan_success_nfc_message" = "NFC Chip Info\nVerified";
"FaceTec_result_idscan_skip_or_error_nfc_message" = "NFC Scan Info\nUploaded";
"FaceTec_result_idscan_retry_face_did_not_match_message" = "Face Didn't Match\nHighly Enough";
"FaceTec_result_idscan_retry_id_not_fully_visible_message" = "ID Document\nNot Fully Visible";
"FaceTec_result_idscan_retry_ocr_results_not_good_enough_message" = "ID Text Not Legible";
"FaceTec_result_idscan_retry_id_type_not_supported_message" = "ID Type Not Supported\nPlease Use a Different ID";

"FaceTec_retry_header" = "Let's Try That Again";
"FaceTec_retry_subheader_message" = "We Need a Clearer Video Selfie";
"FaceTec_retry_instruction_message_1" = "Neutral Expression, No Smiling";
"FaceTec_retry_instruction_message_2" = "No Glare or Extreme Lighting";
"FaceTec_retry_instruction_message_3" = "Too Blurry, Clean Your Camera";
"FaceTec_retry_your_image_label" = "Your Selfie";
"FaceTec_retry_ideal_image_label" = "Ideal Pose";

// MARK: - Ekyc review screen

// Title label
"Ekyc_review_main_title" = "Review and Confirm";
"Ekyc_review_header_title" = "Information";
"Ekyc_review_title_name_en" = "Title (ENG)*";
"Ekyc_review_title_name_th" = "Title (TH)*";
"Ekyc_review_first_name_en" = "First Name (ENG)*";
"Ekyc_review_first_name_th" = "First Name (TH)*";
"Ekyc_review_middle_name_en" = "Middle Name (ENG)\n(if applicable)";
"Ekyc_review_middle_name_th" = "Middle Name (TH)\n(if applicable)";
"Ekyc_review_last_name_en" = "Last Name (ENG)*";
"Ekyc_review_last_name_th" = "Last Name (TH)*";
"Ekyc_review_card_id_number" = "National ID Number*";
"Ekyc_review_laser_id_number" = "Laser ID*";
"Ekyc_review_date_of_birth" = "Birth Date\n(A.D. i.e. 2022)*";
"Ekyc_review_date_of_issue" = "Issue Date\n(A.D. i.e. 2022)*";
"Ekyc_review_date_of_expire" = "Expiry Date\n(A.D. i.e. 2022)*";
"Ekyc_review_confirm" = "Confirm Information";

// Error message
"Ekyc_review_require" = "Required";
"Ekyc_review_expire_card" = "Card is expired";
"Ekyc_review_exceed_card" = "Issued date cannot be greater than today's date";
"Ekyc_review_allow_en" = "Allow only English characters";
"Ekyc_review_allow_th" = "Allow only Thai characters";
"Ekyc_review_allow_number" = "Allow only numberic";
"Ekyc_review_allow_laser" = "Laser ID cannot be less than 12 characters";
"Ekyc_review_allow_en_and_number" = "Allow only English capital letters or numbers";
"Ekyc_review_dialog_description" = "Please check your information before submitting";
"Ekyc_review_confirm_dialog" = "Confirm";
"Ekyc_review_cancel_dialog" = "Cancel";
"Ekyc_review_date_of_birth_not_avaliable" = "Please correct the error.\nIf date or month of birth is not available, please fill in -";
"Ekyc_review_correct_error" = "Please correct the error.";
"Ekyc_review_allow_card_id" = "Please double-check your National ID";
"Ekyc_review_expire_within_seven_days" = "Your national ID card is not valid. Please renew your ID card.";
"Ekyc_review_check_sum_card_id" = "Please double-check your National ID";

// Placeholder textfield
"Ekyc_review_title_name_th_placeholder" = "Please enter your title in Thai.";
"Ekyc_review_first_name_th_placeholder" = "Please enter your first name in Thai.";
"Ekyc_review_last_name_th_placeholder" = "Please enter your last name in Thai.";
"Ekyc_review_title_name_en_placeholder" = "Please enter your title in English.";
"Ekyc_review_first_name_en_placeholder" = "Please enter your first name in English.";
"Ekyc_review_last_name_en_placeholder" = "Please enter your last name in English.";
"Ekyc_review_card_id_number_placeholder" = "Please enter your national ID number.";
"Ekyc_review_laser_id_number_placeholder" = "Please enter your laser ID.";
"Ekyc_review_date_of_birth_placeholder" = "Please enter your date of birth";
"Ekyc_review_date_of_issue_placeholder" = "Please enter your national ID issue date";
"Ekyc_review_date_of_expire_placeholder" = "Please enter your national ID expiry date";
"Ekyc_review_no_expire_date_placeholder" = "No expiry date";
"Ekyc_review_middle_name_th_placeholder" = "";
"Ekyc_review_middle_name_en_placeholder" = "";
"Ekyc_review_day_placeholder" = "DD";
"Ekyc_review_month_placeholder" = "MM";
"Ekyc_review_year_placeholder" = "YYYY";

// MARK: - Ekyc NDID IDP list screen

// Navigation title
"Ekyc_ndid_navigation_title" = "Open an SCBS account";

// IDP label
"Ekyc_ndid_idp_title" = "Choose an Identity Provider to register for NDID service";
"Ekyc_ndid_idp_detail" = "Please choose an Identity Provider to register NDID Service (you should already have mobile application with that Identity Provider)";
"Ekyc_ndid_idp_registered" = "Identity Providers you have previously registered with (Additional verification with the Identity Provider is not required):";
"Ekyc_ndid_idp_unregistered" = "Identity Providers that allow on-the-fly uplifting and enrollment";
"Ekyc_ndid_idp_cancel" = "Cancel";
"Ekyc_ndid_idp_next" = "Next";
"Ekyc_ndid_idp_error" = "You have not registered to use NDID Service. Please register NDID Service first.";

// MARK: - Ekyc NDID Holding screen

"Ekyc_ndid_holding_title" = "NDID";
"Ekyc_ndid_holding_authenticate_title" = "Please go to the selected Identity Provider's mobile application to authenticate your identity";
"Ekyc_ndid_holding_authenticate_detail" = "You are authenticating you identity in accordance with SCBS' objective and are agreeing to send back your information from %@";
"Ekyc_ndid_holding_verify_identification" = "Please verify your identification at %@ mobile application within 60 minutes and return to continue your transaction here";
"Ekyc_ndid_holding_open" = "Open %@";
"Ekyc_ndid_holding_referralCode" = "Ref Code: %@";
"Ekyc_ndid_holding_cancel_button" = "Cancel";
"Ekyc_ndid_holding_confirm_cancel" = "Do you want to cancel identification?";
"Ekyc_ndid_holding_confirm" = "I have verified myself";
"Ekyc_ndid_holding_deeplink_alert" = "Please verify your identification at IdP's mobile application";
"Ekyc_ndid_holding_canceled_alert" = "You have canceled the request or changed the Identity Provider, please select another Identity Provider.";
"Ekyc_ndid_holding_proceed_error" = "Sorry, your request cannot be proceeded at the moment. Please try again";
"Ekyc_ndid_holding_identification_error" = "You haven't completed an identification. Please complete the identification at the selected Identity Provider's mobile application before proceeding.";
"Ekyc_ndid_holding_idp_reject" = "You have rejected your identity verification. Please retry or select another Identity Provider.";

// alert button label

"Ekyc_ndid_cancel" = "Cancel";
"Ekyc_ndid_next" = "Next";
"Ekyc_ndid_ok" = "OK";
"Ekyc_ndid_yes" = "Yes";
"Ekyc_ndid_no" = "No";

// MARK: - Ekyc NDID Success screen

"Ekyc_ndid_successful_title" = "Successfully identity authentication.";
"Ekyc_ndid_successful_detail" = "The request to open an account has been sent.";
"Ekyc_ndid_successful_referralCode" = "Ref Code: %@";
"Ekyc_ndid_successful_button" = "Next";

// MARK: - Ekyc Verified DOPA

"Ekyc_dopa_fail" = "You are not eligible to open an account. Please check your ID card information and try again.";
"Ekyc_dopa_limit_reached" = "You are not eligible to open an account. Please contact any SCB branches for further inquiry.";

// MARK: - Ekyc OcrIdCard Enable camera screen

"Ekyc_camera_permission_header" = "Enable Camera";
"Ekyc_camera_permission_message_enroll" = "Please click the button below\nto enable your camera.";
"Ekyc_camera_permission_enable_camera" = "ENABLE CAMERA";
