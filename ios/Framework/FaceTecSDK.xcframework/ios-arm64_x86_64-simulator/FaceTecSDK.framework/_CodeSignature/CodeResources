<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FaceTecPublicApi.h</key>
		<data>
		OZJevchKVZouBZdYThuWMkjyJe0=
		</data>
		<key>Headers/FaceTecPublicStringKeys.h</key>
		<data>
		e/3MIsDRKm0b/0rZRAD7Ml/YNKA=
		</data>
		<key>Headers/FaceTecSDK.h</key>
		<data>
		JeIjaPESffWvy5ie4QT25+9H8Mw=
		</data>
		<key>Info.plist</key>
		<data>
		AtcWGVYelNZlVK+Ed0/nnX39EdE=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		+zXAVfgy+i2m1qE+GQYEKlNaYzc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FaceTecPublicApi.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SFYzkt3QTGpZpj6TqPSqqfyRrzXjei1McHb7ADQbHFQ=
			</data>
		</dict>
		<key>Headers/FaceTecPublicStringKeys.h</key>
		<dict>
			<key>hash2</key>
			<data>
			44pJyih4Rw/L0Xj4h8ZfF6EYj4YfK8p148jcMCZrd90=
			</data>
		</dict>
		<key>Headers/FaceTecSDK.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Ibte2KAl35oMhi6DQucqH33FcJTzAontEXiKeIE3Tas=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			r82o20tt0ditmsziXA7PVOz6ZALGqbZwdOW1letF5ps=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
