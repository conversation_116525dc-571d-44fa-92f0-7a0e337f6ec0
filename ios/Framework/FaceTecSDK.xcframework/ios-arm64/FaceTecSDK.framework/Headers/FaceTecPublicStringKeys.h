
extern NSString *const FaceTecStringKeyAccessibilityCancelButton;
extern NSString *const FaceTecStringKeyAccessibilityTorchButton;
extern NSString *const FaceTecStringKeyAccessibilityTapGuidance;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackMovePhoneAway;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackMovePhoneCloser;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceTooFarLeft;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceTooFarRight;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceTooLow;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceTooHigh;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceRotatedTooFarLeft;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceRotatedTooFarRight;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFacePointingTooFarLeft;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFacePointingTooFarRight;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackFaceNotOnCamera;
extern NSString *const FaceTecStringKeyAccessibilityFeedbackHoldDeviceToEyeLevel;
extern NSString *const FaceTecStringKeyActionOk;
extern NSString *const FaceTecStringKeyActionImReady;
extern NSString *const FaceTecStringKeyActionTryAgain;
extern NSString *const FaceTecStringKeyActionContinue;
extern NSString *const FaceTecStringKeyActionTakePhoto;
extern NSString *const FaceTecStringKeyActionRetakePhoto;
extern NSString *const FaceTecStringKeyActionAcceptPhoto;
extern NSString *const FaceTecStringKeyActionConfirm;
extern NSString *const FaceTecStringKeyActionScanNfc;
extern NSString *const FaceTecStringKeyActionScanNfcCard;
extern NSString *const FaceTecStringKeyActionSkipNfc;
extern NSString *const FaceTecStringKeyCameraPermissionHeader;
extern NSString *const FaceTecStringKeyCameraPermissionMessageEnroll;
extern NSString *const FaceTecStringKeyCameraPermissionMessageAuth;
extern NSString *const FaceTecStringKeyCameraPermissionEnableCamera;
extern NSString *const FaceTecStringKeyCameraPermissionLaunchSettings;
extern NSString *const FaceTecStringKeyFeedbackCenterFace;
extern NSString *const FaceTecStringKeyFeedbackFaceNotFound;
extern NSString *const FaceTecStringKeyFeedbackFaceNotLookingStraightAhead;
extern NSString *const FaceTecStringKeyFeedbackFaceNotUpright;
extern NSString *const FaceTecStringKeyFeedbackHoldSteady;
extern NSString *const FaceTecStringKeyFeedbackMovePhoneAway;
extern NSString *const FaceTecStringKeyFeedbackMovePhoneCloser;
extern NSString *const FaceTecStringKeyFeedbackMovePhoneToEyeLevel;
extern NSString *const FaceTecStringKeyFeedbackUseEvenLighting;
extern NSString *const FaceTecStringKeyIdscanTypeSelectionHeader;
extern NSString *const FaceTecStringKeyIdscanCaptureTapToFocusMessage;
extern NSString *const FaceTecStringKeyIdscanCaptureHoldSteadyMessage;
extern NSString *const FaceTecStringKeyIdscanCaptureIdFrontInstructionMessage;
extern NSString *const FaceTecStringKeyIdscanCaptureIdBackInstructionMessage;
extern NSString *const FaceTecStringKeyIdscanReviewIdFrontInstructionMessage;
extern NSString *const FaceTecStringKeyIdscanReviewIdBackInstructionMessage;
extern NSString *const FaceTecStringKeyIdscanOcrConfirmationMainHeader;
extern NSString *const FaceTecStringKeyIdscanOcrConfirmationScrollMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusDisabledMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusReadyMessage;
extern NSString *const FaceTecStringKeyIdscanNfcCardStatusReadyMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusStartingMessage;
extern NSString *const FaceTecStringKeyIdscanNfcCardStatusStartingMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusScanningMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusWeakConnectionMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusFinishedWithSuccessMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusFinishedWithErrorMessage;
extern NSString *const FaceTecStringKeyIdscanNfcCardStatusFinishedWithErrorMessage;
extern NSString *const FaceTecStringKeyIdscanNfcStatusSkippedMessage;
extern NSString *const FaceTecStringKeyIdscanAdditionalReviewMessage;
extern NSString *const FaceTecStringKeyIdscanFeedbackFlipIdToBackMessage;
extern NSString *const FaceTecStringKeyInitializingCamera;
extern NSString *const FaceTecStringKeyInstructionsHeaderReady1;
extern NSString *const FaceTecStringKeyInstructionsHeaderReady2;
extern NSString *const FaceTecStringKeyInstructionsMessageReady1;
extern NSString *const FaceTecStringKeyInstructionsMessageReady2;
extern NSString *const FaceTecStringKeyPresessionFrameYourFace;
extern NSString *const FaceTecStringKeyPresessionPositionFaceStraightInOval;
extern NSString *const FaceTecStringKeyPresessionHoldSteady3;
extern NSString *const FaceTecStringKeyPresessionHoldSteady2;
extern NSString *const FaceTecStringKeyPresessionHoldSteady1;
extern NSString *const FaceTecStringKeyPresessionRemoveDarkGlasses;
extern NSString *const FaceTecStringKeyPresessionNeutralExpression;
extern NSString *const FaceTecStringKeyPresessionConditionsTooBright;
extern NSString *const FaceTecStringKeyPresessionBrightenYourEnvironment;
extern NSString *const FaceTecStringKeyResultFacescanUploadMessage;
extern NSString *const FaceTecStringKeyResultSuccessMessage;
extern NSString *const FaceTecStringKeyResultIdscanUploadMessage;
extern NSString *const FaceTecStringKeyResultNfcUploadMessage;
extern NSString *const FaceTecStringKeyResultIdscanUnsuccessMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessFrontSideMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessFrontSideBackNextMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessFrontSideNfcNextMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessBackSideMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessBackSideNfcNextMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessPassportMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessPassportNfcNextMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessUserConfirmationMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessNfcMessage;
extern NSString *const FaceTecStringKeyResultIdscanSuccessAdditionalReviewMessage;
extern NSString *const FaceTecStringKeyResultIdscanSkipOrErrorNfcMessage;
extern NSString *const FaceTecStringKeyResultIdscanRetryFaceDidNotMatchMessage;
extern NSString *const FaceTecStringKeyResultIdscanRetryIdNotFullyVisibleMessage;
extern NSString *const FaceTecStringKeyResultIdscanRetryOcrResultsNotGoodEnoughMessage;
extern NSString *const FaceTecStringKeyResultIdscanRetryIdTypeNotSupportedMessage;
extern NSString *const FaceTecStringKeyResultIdscanRetryBarcodeNotReadMessage;
extern NSString *const FaceTecStringKeyRetryHeader;
extern NSString *const FaceTecStringKeyRetrySubheaderMessage;
extern NSString *const FaceTecStringKeyRetryInstructionMessage1;
extern NSString *const FaceTecStringKeyRetryInstructionMessage2;
extern NSString *const FaceTecStringKeyRetryInstructionMessage3;
extern NSString *const FaceTecStringKeyRetryYourImageLabel;
extern NSString *const FaceTecStringKeyRetryIdealImageLabel;
