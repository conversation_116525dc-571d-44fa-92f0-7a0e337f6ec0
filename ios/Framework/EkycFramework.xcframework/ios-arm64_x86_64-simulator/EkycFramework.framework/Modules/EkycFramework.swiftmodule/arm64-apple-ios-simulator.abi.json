{"ABIRoot": {"kind": "Root", "name": "EkycFramework", "printedName": "EkycFramework", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "TypeDecl", "name": "EkycFrameworkKit", "printedName": "EkycFrameworkKit", "children": [{"kind": "TypeDecl", "name": "NdidError", "printedName": "NdidError", "children": [{"kind": "Var", "name": "code", "printedName": "code", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:13EkycFramework0aB3KitC9NdidErrorV4codeSSSgvp", "mangledName": "$s13EkycFramework0aB3KitC9NdidErrorV4codeSSSgvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework0aB3KitC9NdidErrorV4codeSSSgvg", "mangledName": "$s13EkycFramework0aB3KitC9NdidErrorV4codeSSSgvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}]}], "declKind": "Struct", "usr": "s:13EkycFramework0aB3KitC9NdidErrorV", "mangledName": "$s13EkycFramework0aB3KitC9NdidErrorV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "NdidData", "printedName": "NdidData", "children": [{"kind": "Var", "name": "referenceId", "printedName": "referenceId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:13EkycFramework0aB3KitC8NdidDataV11referenceIdSSSgvp", "mangledName": "$s13EkycFramework0aB3KitC8NdidDataV11referenceIdSSSgvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework0aB3KitC8NdidDataV11referenceIdSSSgvg", "mangledName": "$s13EkycFramework0aB3KitC8NdidDataV11referenceIdSSSgvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "requestId", "printedName": "requestId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:13EkycFramework0aB3KitC8NdidDataV9requestIdSSSgvp", "mangledName": "$s13EkycFramework0aB3KitC8NdidDataV9requestIdSSSgvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework0aB3KitC8NdidDataV9requestIdSSSgvg", "mangledName": "$s13EkycFramework0aB3KitC8NdidDataV9requestIdSSSgvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}]}], "declKind": "Struct", "usr": "s:13EkycFramework0aB3KitC8NdidDataV", "mangledName": "$s13EkycFramework0aB3KitC8NdidDataV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Function", "name": "initEkyc", "printedName": "initEkyc(sessionId:token:environment:customizeTheme:customizeHost:language:callBack:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.CustomizeTheme?", "children": [{"kind": "TypeNominal", "name": "CustomizeTheme", "printedName": "EkycFramework.CustomizeTheme", "usr": "s:********************************"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.String?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.String?)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC04initA09sessionId5token11environment14customizeTheme0I4Host8language8callBackySS_S2SAA09CustomizeJ0VSgSSSgAOySb_SSAOtctFZ", "mangledName": "$s13EkycFramework0aB3KitC04initA09sessionId5token11environment14customizeTheme0I4Host8language8callBackySS_S2SAA09CustomizeJ0VSgSSSgAOySb_SSAOtctFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "ocrIdCardVerifyByFace", "printedName": "ocrIdCardVerifyByFace(fromViewController:checkExpiredIdCard:checkDopa:enableConfirmInfo:ocrPrefill:callBack:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.OcrPrefill?", "children": [{"kind": "TypeNominal", "name": "OcrPrefill", "printedName": "EkycFramework.OcrPrefill", "usr": "s:13EkycFramework10OcrPrefillV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>, <PERSON>, EkycFramework.DocumentAPIData?, EkycFramework.DocumentAPIData?, EkycFramework.DopaData?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, EkycFramework.DocumentAPIData?, EkycFramework.DocumentAPIData?, EkycFramework.DopaData?)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DocumentAPIData?", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DocumentAPIData?", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DopaData?", "children": [{"kind": "TypeNominal", "name": "DopaData", "printedName": "EkycFramework.DopaData", "usr": "s:13EkycFramework8DopaDataV"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC21ocrIdCardVerifyByFace18fromViewController012checkExpiredeF00M4Dopa17enableConfirmInfo0D7Prefill8callBackySo06UIViewL0C_SbSgA2mA03OcrS0VSgySb_SSAA15DocumentAPIDataVSgAsA0O4DataVSgtctFZ", "mangledName": "$s13EkycFramework0aB3KitC21ocrIdCardVerifyByFace18fromViewController012checkExpiredeF00M4Dopa17enableConfirmInfo0D7Prefill8callBackySo06UIViewL0C_SbSgA2mA03OcrS0VSgySb_SSAA15DocumentAPIDataVSgAsA0O4DataVSgtctFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "livenessCheck", "printedName": "livenessCheck(fromViewController:callBack:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>, <PERSON>.String) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, Swift.String)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}]}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC13livenessCheck18fromViewController8callBackySo06UIViewH0C_ySb_SStctFZ", "mangledName": "$s13EkycFramework0aB3KitC13livenessCheck18fromViewController8callBackySo06UIViewH0C_ySb_SStctFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "presentReviewDocument", "printedName": "presentReviewDocument(viewController:responseData:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC21presentReviewDocument14viewController12responseDataySo06UIViewH0C_SStFZ", "mangledName": "$s13EkycFramework0aB3KitC21presentReviewDocument14viewController12responseDataySo06UIViewH0C_SStFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "ndidVerification", "printedName": "ndidVerification(fromViewController:identifierType:identifierValue:serviceId:callBack:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, Swift.String?, EkycFramework.EkycFrameworkKit.NdidError?, EkycFramework.EkycFrameworkKit.NdidData?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, Swift.String?, EkycFramework.EkycFrameworkKit.NdidError?, EkycFramework.EkycFrameworkKit.NdidData?)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.EkycFrameworkKit.NdidError?", "children": [{"kind": "TypeNominal", "name": "NdidError", "printedName": "EkycFramework.EkycFrameworkKit.NdidError", "usr": "s:13EkycFramework0aB3KitC9NdidErrorV"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.EkycFrameworkKit.NdidData?", "children": [{"kind": "TypeNominal", "name": "NdidData", "printedName": "EkycFramework.EkycFrameworkKit.NdidData", "usr": "s:13EkycFramework0aB3KitC8NdidDataV"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC16ndidVerification18fromViewController14identifierType0I5Value9serviceId8callBackySo06UIViewH0C_S3SySb_S2SSgAC9NdidErrorVSgAC0Q4DataVSgtctFZ", "mangledName": "$s13EkycFramework0aB3KitC16ndidVerification18fromViewController14identifierType0I5Value9serviceId8callBackySo06UIViewH0C_S3SySb_S2SSgAC9NdidErrorVSgAC0Q4DataVSgtctFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "ocrIdCard", "printedName": "ocrIdCard(fromViewController:checkExpiredIdCard:checkDopa:enableConfirmInfo:ocrPrefill:callBack:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.OcrPrefill?", "children": [{"kind": "TypeNominal", "name": "OcrPrefill", "printedName": "EkycFramework.OcrPrefill", "usr": "s:13EkycFramework10OcrPrefillV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>, <PERSON>, EkycFramework.DocumentAPIData?, EkycFramework.DocumentAPIData?, EkycFramework.DopaData?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, <PERSON><PERSON>, EkycFramework.DocumentAPIData?, EkycFramework.DocumentAPIData?, EkycFramework.DopaData?)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DocumentAPIData?", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DocumentAPIData?", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.DopaData?", "children": [{"kind": "TypeNominal", "name": "DopaData", "printedName": "EkycFramework.DopaData", "usr": "s:13EkycFramework8DopaDataV"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC9ocrIdCard18fromViewController012checkExpiredeF00J4Dopa17enableConfirmInfo0D7Prefill8callBackySo06UIViewI0C_SbSgA2mA03OcrP0VSgySb_SSAA15DocumentAPIDataVSgAsA0L4DataVSgtctFZ", "mangledName": "$s13EkycFramework0aB3KitC9ocrIdCard18fromViewController012checkExpiredeF00J4Dopa17enableConfirmInfo0D7Prefill8callBackySo06UIViewI0C_SbSgA2mA03OcrP0VSgySb_SSAA15DocumentAPIDataVSgAsA0L4DataVSgtctFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getInstallationId", "printedName": "getInstallationId()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:13EkycFramework0aB3KitC17getInstallationIdSSSgyFZ", "mangledName": "$s13EkycFramework0aB3KitC17getInstallationIdSSSgyFZ", "moduleName": "EkycFramework", "static": true, "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:13EkycFramework0aB3KitC", "mangledName": "$s13EkycFramework0aB3KitC", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "DopaData", "printedName": "DopaData", "children": [{"kind": "Var", "name": "code", "printedName": "code", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:13EkycFramework8DopaDataV4codeSSSgvp", "mangledName": "$s13EkycFramework8DopaDataV4codeSSSgvp", "moduleName": "EkycFramework", "declAttributes": ["HasInitialValue", "HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4codeSSSgvg", "mangledName": "$s13EkycFramework8DopaDataV4codeSSSgvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4codeSSSgvs", "mangledName": "$s13EkycFramework8DopaDataV4codeSSSgvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4codeSSSgvM", "mangledName": "$s13EkycFramework8DopaDataV4codeSSSgvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "desc", "printedName": "desc", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:13EkycFramework8DopaDataV4descSSSgvp", "mangledName": "$s13EkycFramework8DopaDataV4descSSSgvp", "moduleName": "EkycFramework", "declAttributes": ["HasInitialValue", "HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4descSSSgvg", "mangledName": "$s13EkycFramework8DopaDataV4descSSSgvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4descSSSgvs", "mangledName": "$s13EkycFramework8DopaDataV4descSSSgvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework8DopaDataV4descSSSgvM", "mangledName": "$s13EkycFramework8DopaDataV4descSSSgvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(code:desc:)", "children": [{"kind": "TypeNominal", "name": "DopaData", "printedName": "EkycFramework.DopaData", "usr": "s:13EkycFramework8DopaDataV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework8DopaDataV4code4descACSSSg_AFtcfc", "mangledName": "$s13EkycFramework8DopaDataV4code4descACSSSg_AFtcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework8DopaDataV", "mangledName": "$s13EkycFramework8DopaDataV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "DocumentAPIData", "printedName": "DocumentAPIData", "children": [{"kind": "Var", "name": "nationalId", "printedName": "nationalId", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV10nationalIdSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV10nationalIdSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10nationalIdSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV10nationalIdSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10nationalIdSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV10nationalIdSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10nationalIdSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV10nationalIdSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "titleTh", "printedName": "titleTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV7titleThSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleThSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleThSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleThSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "titleEn", "printedName": "titleEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV7titleEnSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleEnSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleEnSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7titleEnSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV7titleEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "firstNameTh", "printedName": "firstNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameThSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameThSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameThSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameThSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "firstNameEn", "printedName": "firstNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameEnSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameEnSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameEnSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11firstNameEnSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV11firstNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "middleNameTh", "printedName": "middleNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameThSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameThSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameThSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameThSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "middleNameEn", "printedName": "middleNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameEnSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameEnSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameEnSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12middleNameEnSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV12middleNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "lastNameTh", "printedName": "lastNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameThSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameThSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameThSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameThSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "lastNameEn", "printedName": "lastNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameEnSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameEnSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameEnSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV10lastNameEnSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV10lastNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfBirth", "printedName": "dateOfBirth", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfBirthSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfBirthSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfBirthSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfBirthSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfBirthSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfBirthSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfBirthSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfBirthSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfIssue", "printedName": "dateOfIssue", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfIssueSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfIssueSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfIssueSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfIssueSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfIssueSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfIssueSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV11dateOfIssueSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV11dateOfIssueSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfExpiry", "printedName": "dateOfExpiry", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV12dateOfExpirySSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV12dateOfExpirySSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12dateOfExpirySSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV12dateOfExpirySSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12dateOfExpirySSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV12dateOfExpirySSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV12dateOfExpirySSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV12dateOfExpirySSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "laserId", "printedName": "laserId", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework15DocumentAPIDataV7laserIdSSvp", "mangledName": "$s13EkycFramework15DocumentAPIDataV7laserIdSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7laserIdSSvg", "mangledName": "$s13EkycFramework15DocumentAPIDataV7laserIdSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7laserIdSSvs", "mangledName": "$s13EkycFramework15DocumentAPIDataV7laserIdSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework15DocumentAPIDataV7laserIdSSvM", "mangledName": "$s13EkycFramework15DocumentAPIDataV7laserIdSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(nationalId:titleTh:titleEn:firstNameTh:firstNameEn:middleNameTh:middleNameEn:lastNameTh:lastNameEn:dateOfBirth:dateOfIssue:dateOfExpiry:laserId:)", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework15DocumentAPIDataV10nationalId7titleTh0G2En09firstNameH00jkI006middlekH00lkI004lastkH00mkI011dateOfBirth0nO5Issue0nO6Expiry05laserF0ACSS_S12Stcfc", "mangledName": "$s13EkycFramework15DocumentAPIDataV10nationalId7titleTh0G2En09firstNameH00jkI006middlekH00lkI004lastkH00mkI011dateOfBirth0nO5Issue0nO6Expiry05laserF0ACSS_S12Stcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(from:)", "children": [{"kind": "TypeNominal", "name": "DocumentAPIData", "printedName": "EkycFramework.DocumentAPIData", "usr": "s:13EkycFramework15DocumentAPIDataV"}, {"kind": "TypeNominal", "name": "Decoder", "printedName": "any Swift.Decoder", "usr": "s:s7DecoderP"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework15DocumentAPIDataV4fromACs7Decoder_p_tKcfc", "mangledName": "$s13EkycFramework15DocumentAPIDataV4fromACs7Decoder_p_tKcfc", "moduleName": "EkycFramework", "implicit": true, "throwing": true, "init_kind": "Designated"}, {"kind": "Function", "name": "encode", "printedName": "encode(to:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Encoder", "printedName": "any Swift.Encoder", "usr": "s:s7EncoderP"}], "declKind": "Func", "usr": "s:13EkycFramework15DocumentAPIDataV6encode2toys7Encoder_p_tKF", "mangledName": "$s13EkycFramework15DocumentAPIDataV6encode2toys7Encoder_p_tKF", "moduleName": "EkycFramework", "implicit": true, "throwing": true, "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:13EkycFramework15DocumentAPIDataV", "mangledName": "$s13EkycFramework15DocumentAPIDataV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}]}, {"kind": "TypeDecl", "name": "DocumentValidateData", "printedName": "DocumentValidateData", "children": [{"kind": "Var", "name": "idCardNumber", "printedName": "idCardNumber", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV12idCardNumberSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV12idCardNumberSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12idCardNumberSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV12idCardNumberSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12idCardNumberSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV12idCardNumberSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12idCardNumberSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV12idCardNumberSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "titleTh", "printedName": "titleTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV7titleThSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleThSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleThSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleThSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "titleEn", "printedName": "titleEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV7titleEnSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleEnSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleEnSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7titleEnSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV7titleEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "firstNameTh", "printedName": "firstNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameThSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameThSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameThSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameThSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "firstNameEn", "printedName": "firstNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameEnSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameEnSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameEnSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11firstNameEnSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV11firstNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "middleNameEn", "printedName": "middleNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameEnSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameEnSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameEnSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameEnSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "middleNameTh", "printedName": "middleNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameThSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameThSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameThSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12middleNameThSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV12middleNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "lastNameTh", "printedName": "lastNameTh", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameThSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameThSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameThSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameThSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameThSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameThSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameThSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameThSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "lastNameEn", "printedName": "lastNameEn", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameEnSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameEnSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameEnSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameEnSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameEnSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameEnSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV10lastNameEnSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV10lastNameEnSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfBirth", "printedName": "dateOfBirth", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfBirthSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfBirthSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfBirthSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfBirthSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfBirthSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfBirthSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfBirthSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfBirthSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfIssue", "printedName": "dateOfIssue", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfIssueSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfIssueSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfIssueSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfIssueSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfIssueSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfIssueSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV11dateOfIssueSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV11dateOfIssueSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dateOfExpire", "printedName": "dateOfExpire", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV12dateOfExpireSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV12dateOfExpireSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12dateOfExpireSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV12dateOfExpireSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12dateOfExpireSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV12dateOfExpireSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV12dateOfExpireSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV12dateOfExpireSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "laserId", "printedName": "laserId", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:13EkycFramework20DocumentValidateDataV7laserIdSSvp", "mangledName": "$s13EkycFramework20DocumentValidateDataV7laserIdSSvp", "moduleName": "EkycFramework", "declAttributes": ["HasStorage", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7laserIdSSvg", "mangledName": "$s13EkycFramework20DocumentValidateDataV7laserIdSSvg", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7laserIdSSvs", "mangledName": "$s13EkycFramework20DocumentValidateDataV7laserIdSSvs", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:13EkycFramework20DocumentValidateDataV7laserIdSSvM", "mangledName": "$s13EkycFramework20DocumentValidateDataV7laserIdSSvM", "moduleName": "EkycFramework", "implicit": true, "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(idCardNumber:titleTh:titleEn:firstNameTh:firstNameEn:middleNameTh:middleNameEn:lastNameTh:lastNameEn:dateOfBirth:dateOfIssue:dateOfExpire:laserId:)", "children": [{"kind": "TypeNominal", "name": "DocumentValidateData", "printedName": "EkycFramework.DocumentValidateData", "usr": "s:13EkycFramework20DocumentValidateDataV"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework20DocumentValidateDataV12idCardNumber7titleTh0I2En09firstNameJ00lmK006middlemJ00nmK004lastmJ00omK011dateOfBirth0pQ5Issue0pQ6Expire7laserIdACSS_S12Stcfc", "mangledName": "$s13EkycFramework20DocumentValidateDataV12idCardNumber7titleTh0I2En09firstNameJ00lmK006middlemJ00nmK004lastmJ00omK011dateOfBirth0pQ5Issue0pQ6Expire7laserIdACSS_S12Stcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework20DocumentValidateDataV", "mangledName": "$s13EkycFramework20DocumentValidateDataV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "JSONValue", "printedName": "JSONValue", "declKind": "Protocol", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "JSONRoot", "printedName": "JSONRoot", "declKind": "Protocol", "usr": "s:13EkycFramework8JSONRootP", "mangledName": "$s13EkycFramework8JSONRootP", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "AVFoundation", "printedName": "AVFoundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "TypeDecl", "name": "CustomizeTheme", "printedName": "CustomizeTheme", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(text:button:oval:ocr:image:border:ndid:other:)", "children": [{"kind": "TypeNominal", "name": "CustomizeTheme", "printedName": "EkycFramework.CustomizeTheme", "usr": "s:********************************"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Text?", "children": [{"kind": "TypeNominal", "name": "Text", "printedName": "EkycFramework.Text", "usr": "s:13EkycFramework4TextV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Button?", "children": [{"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "EkycFramework.Button", "usr": "s:13EkycFramework6ButtonV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Oval?", "children": [{"kind": "TypeNominal", "name": "Oval", "printedName": "EkycFramework.Oval", "usr": "s:13EkycFramework4OvalV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Ocr?", "children": [{"kind": "TypeNominal", "name": "Ocr", "printedName": "EkycFramework.Ocr", "usr": "s:13EkycFramework3OcrV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Image?", "children": [{"kind": "TypeNominal", "name": "Image", "printedName": "EkycFramework.Image", "usr": "s:13EkycFramework5ImageV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Border?", "children": [{"kind": "TypeNominal", "name": "Border", "printedName": "EkycFramework.Border", "usr": "s:13EkycFramework6BorderV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Ndid?", "children": [{"kind": "TypeNominal", "name": "Ndid", "printedName": "EkycFramework.Ndid", "usr": "s:13EkycFramework4NdidV"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.Other?", "children": [{"kind": "TypeNominal", "name": "Other", "printedName": "EkycFramework.Other", "usr": "s:13EkycFramework5OtherV"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:********************************4text6button4oval3ocr5image6border4ndid5otherAcA4TextVSg_AA6ButtonVSgAA4OvalVSgAA3OcrVSgAA5ImageVSgAA6BorderVSgAA4NdidVSgAA5OtherVSgtcfc", "mangledName": "$s********************************4text6button4oval3ocr5image6border4ndid5otherAcA4TextVSg_AA6ButtonVSgAA4OvalVSgAA3OcrVSgAA5ImageVSgAA6BorderVSgAA4NdidVSgAA5OtherVSgtcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:********************************", "mangledName": "$s********************************", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Text", "printedName": "Text", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(fontName:linkColor:primaryTextColor:secondaryTextColor:secondaryTextBackgroundColor:)", "children": [{"kind": "TypeNominal", "name": "Text", "printedName": "EkycFramework.Text", "usr": "s:13EkycFramework4TextV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIFont?", "children": [{"kind": "TypeNominal", "name": "UIFont", "printedName": "UIKit.UIFont", "usr": "c:objc(cs)UIFont"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework4TextV8fontName9linkColor07primarycG009secondarycG00ic10BackgroundG0ACSo6UIFontCSg_So7UIColorCSgA3Ntcfc", "mangledName": "$s13EkycFramework4TextV8fontName9linkColor07primarycG009secondarycG00ic10BackgroundG0ACSo6UIFontCSg_So7UIColorCSgA3Ntcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework4TextV", "mangledName": "$s13EkycFramework4TextV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(normalTextColor:normalBackgroundColor:disabledTextColor:disabledBackgroundColor:highlightTextColor:highlightBackgroundColor:outlineTextColor:outlineBackgroundColor:outlineBorderColor:)", "children": [{"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "EkycFramework.Button", "usr": "s:13EkycFramework6ButtonV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework6ButtonV15normalTextColor0d10BackgroundF008disabledeF00hgF009highlighteF00igF007outlineeF00jgF00j6BorderF0ACSo7UIColorCSg_A8Otcfc", "mangledName": "$s13EkycFramework6ButtonV15normalTextColor0d10BackgroundF008disabledeF00hgF009highlighteF00igF007outlineeF00jgF00j6BorderF0ACSo7UIColorCSg_A8Otcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework6ButtonV", "mangledName": "$s13EkycFramework6ButtonV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Ocr", "printedName": "Ocr", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(mainHeaderTextColor:sectionHeaderTextColor:labelTextColor:)", "children": [{"kind": "TypeNominal", "name": "Ocr", "printedName": "EkycFramework.Ocr", "usr": "s:13EkycFramework3OcrV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework3OcrV19mainHeaderTextColor07sectionefG005labelfG0ACSo7UIColorCSg_A2Itcfc", "mangledName": "$s13EkycFramework3OcrV19mainHeaderTextColor07sectionefG005labelfG0ACSo7UIColorCSg_A2Itcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework3OcrV", "mangledName": "$s13EkycFramework3OcrV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Oval", "printedName": "Oval", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(strokeColor:)", "children": [{"kind": "TypeNominal", "name": "Oval", "printedName": "EkycFramework.Oval", "usr": "s:13EkycFramework4OvalV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework4OvalV11strokeColorACSo7UIColorCSg_tcfc", "mangledName": "$s13EkycFramework4OvalV11strokeColorACSo7UIColorCSg_tcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework4OvalV", "mangledName": "$s13EkycFramework4OvalV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Other", "printedName": "Other", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(backgroundColor:progressColor:)", "children": [{"kind": "TypeNominal", "name": "Other", "printedName": "EkycFramework.Other", "usr": "s:13EkycFramework5OtherV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework5OtherV15backgroundColor08progressE0ACSo7UIColorCSg_AHtcfc", "mangledName": "$s13EkycFramework5OtherV15backgroundColor08progressE0ACSo7UIColorCSg_AHtcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework5OtherV", "mangledName": "$s13EkycFramework5OtherV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Image", "printedName": "Image", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(logo:closeImage:idCardImage:permissionCameraImage:activeFlash:inactiveFlash:)", "children": [{"kind": "TypeNominal", "name": "Image", "printedName": "EkycFramework.Image", "usr": "s:13EkycFramework5ImageV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework5ImageV4logo05closeC006idCardC0016permissionCameraC011activeFlash08inactiveK0ACSo7UIImageCSg_A5Ltcfc", "mangledName": "$s13EkycFramework5ImageV4logo05closeC006idCardC0016permissionCameraC011activeFlash08inactiveK0ACSo7UIImageCSg_A5Ltcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework5ImageV", "mangledName": "$s13EkycFramework5ImageV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Border", "printedName": "Border", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(borderColor:selectedBorderColor:)", "children": [{"kind": "TypeNominal", "name": "Border", "printedName": "EkycFramework.Border", "usr": "s:13EkycFramework6BorderV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework6BorderV11borderColor08selectedcE0ACSo7UIColorCSg_AHtcfc", "mangledName": "$s13EkycFramework6BorderV11borderColor08selectedcE0ACSo7UIColorCSg_AHtcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework6BorderV", "mangledName": "$s13EkycFramework6BorderV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "Ndid", "printedName": "Ndid", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(successIcon:errorIcon:timerColor:timerBackgroundColor:)", "children": [{"kind": "TypeNominal", "name": "Ndid", "printedName": "EkycFramework.Ndid", "usr": "s:13EkycFramework4NdidV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIImage?", "children": [{"kind": "TypeNominal", "name": "UIImage", "printedName": "UIKit.UIImage", "usr": "c:objc(cs)UIImage"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIColor?", "children": [{"kind": "TypeNominal", "name": "UIColor", "printedName": "UIKit.UIColor", "usr": "c:objc(cs)UIColor"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework4NdidV11successIcon05errorE010timerColor0g10BackgroundH0ACSo7UIImageCSg_AJSo7UIColorCSgAMtcfc", "mangledName": "$s13EkycFramework4NdidV11successIcon05errorE010timerColor0g10BackgroundH0ACSo7UIImageCSg_AJSo7UIColorCSgAMtcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework4NdidV", "mangledName": "$s13EkycFramework4NdidV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "AVFoundation", "printedName": "AVFoundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "TypeDecl", "name": "OcrIdCardPreLoadViewController", "printedName": "OcrIdCardPreLoadViewController", "children": [{"kind": "Function", "name": "awakeFromNib", "printedName": "awakeFromNib()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController(im)awakeFromNib", "mangledName": "$s13EkycFramework30OcrIdCardPreLoadViewControllerC12awakeFromNibyyF", "moduleName": "EkycFramework", "overriding": true, "objc_name": "awakeFromNib", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Override", "AccessControl", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "viewDidLoad", "printedName": "viewDidLoad()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController(im)viewDidLoad", "mangledName": "$s13EkycFramework30OcrIdCardPreLoadViewControllerC07viewDidG0yyF", "moduleName": "EkycFramework", "overriding": true, "objc_name": "viewDidLoad", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Override", "AccessControl", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(nibName:bundle:)", "children": [{"kind": "TypeNominal", "name": "OcrIdCardPreLoadViewController", "printedName": "EkycFramework.OcrIdCardPreLoadViewController", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.Bundle?", "children": [{"kind": "TypeNominal", "name": "Bundle", "printedName": "Foundation.Bundle", "usr": "c:objc(cs)NSBundle"}], "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController(im)initWithNibName:bundle:", "mangledName": "$s13EkycFramework30OcrIdCardPreLoadViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfc", "moduleName": "EkycFramework", "overriding": true, "implicit": true, "objc_name": "initWithNibName:bundle:", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Override"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(coder:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "EkycFramework.OcrIdCardPreLoadViewController?", "children": [{"kind": "TypeNominal", "name": "OcrIdCardPreLoadViewController", "printedName": "EkycFramework.OcrIdCardPreLoadViewController", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "NSCoder", "printedName": "Foundation.NSCoder", "usr": "c:objc(cs)NSCoder"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController(im)initWithCoder:", "mangledName": "$s13EkycFramework30OcrIdCardPreLoadViewControllerC5coderACSgSo7NSCoderC_tcfc", "moduleName": "EkycFramework", "overriding": true, "implicit": true, "objc_name": "initWithCoder:", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Required"], "init_kind": "Designated"}], "declKind": "Class", "usr": "c:@M@EkycFramework@objc(cs)OcrIdCardPreLoadViewController", "mangledName": "$s13EkycFramework30OcrIdCardPreLoadViewControllerC", "moduleName": "EkycFramework", "declAttributes": ["Preconcurrency", "Custom", "AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)UIViewController", "inheritsConvenienceInitializers": true, "superclassNames": ["UIKit.UIViewController", "UIKit.UIResponder", "ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "UITraitChangeObservable", "printedName": "UITraitChangeObservable", "usr": "s:5UIKit23UITraitChangeObservableP", "mangledName": "$s5UIKit23UITraitChangeObservableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "TypeDecl", "name": "OcrPrefill", "printedName": "OcrPrefill", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(titleThFlag:titleEnFlag:firstNameThFlag:firstNameEnFlag:lastNameThFlag:lastNameEnFlag:dateOfBirthFlag:dateOfIssueFlag:dateOfExpiryFlag:laserIdFlag:)", "children": [{"kind": "TypeNominal", "name": "OcrPrefill", "printedName": "EkycFramework.OcrPrefill", "usr": "s:13EkycFramework10OcrPrefillV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:13EkycFramework10OcrPrefillV11titleThFlag0e2EnG009firstNamefG00ijhG004lastjfG00kjhG0011dateOfBirthG00lm5IssueG00lm6ExpiryG007laserIdG0ACSbSg_A9Ntcfc", "mangledName": "$s13EkycFramework10OcrPrefillV11titleThFlag0e2EnG009firstNamefG00ijhG004lastjfG00kjhG0011dateOfBirthG00lm5IssueG00lm6ExpiryG007laserIdG0ACSbSg_A9Ntcfc", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Struct", "usr": "s:13EkycFramework10OcrPrefillV", "mangledName": "$s13EkycFramework10OcrPrefillV", "moduleName": "EkycFramework", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "EkycFramework", "printedName": "EkycFramework", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["Testable"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "FaceTecSDK", "printedName": "FaceTecSDK", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "EkycFramework", "printedName": "EkycFramework", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["Testable"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "SwiftUI", "printedName": "SwiftUI", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "Import", "name": "DeveloperToolsSupport", "printedName": "DeveloperToolsSupport", "declKind": "Import", "moduleName": "EkycFramework"}, {"kind": "TypeDecl", "name": "Int", "printedName": "Int", "declKind": "Struct", "usr": "s:<PERSON>", "mangledName": "$sSi", "moduleName": "Swift", "declAttributes": ["Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "FixedWidthInteger", "printedName": "FixedWidthInteger", "usr": "s:s17FixedWidthIntegerP", "mangledName": "$ss17FixedWidthIntegerP"}, {"kind": "Conformance", "name": "SignedInteger", "printedName": "SignedInteger", "usr": "s:SZ", "mangledName": "$sSZ"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinIntegerLiteral", "printedName": "_ExpressibleByBuiltinIntegerLiteral", "usr": "s:s35_ExpressibleByBuiltinIntegerLiteralP", "mangledName": "$ss35_ExpressibleByBuiltinIntegerLiteralP"}, {"kind": "Conformance", "name": "BinaryInteger", "printedName": "BinaryInteger", "children": [{"kind": "TypeWitness", "name": "Words", "printedName": "Words", "children": [{"kind": "TypeNominal", "name": "Words", "printedName": "Swift.Int.Words", "usr": "s:Si5WordsV"}]}], "usr": "s:<PERSON>z", "mangledName": "$sSz"}, {"kind": "Conformance", "name": "LosslessStringConvertible", "printedName": "LosslessStringConvertible", "usr": "s:s25LosslessStringConvertibleP", "mangledName": "$ss25LosslessStringConvertibleP"}, {"kind": "Conformance", "name": "SignedNumeric", "printedName": "SignedNumeric", "usr": "s:s13SignedNumericP", "mangledName": "$ss13SignedNumericP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "Numeric", "printedName": "Numeric", "children": [{"kind": "TypeWitness", "name": "Magnitude", "printedName": "Magnitude", "children": [{"kind": "TypeNominal", "name": "UInt", "printedName": "Swift.UInt", "usr": "s:<PERSON>"}]}], "usr": "s:Sj", "mangledName": "$sSj"}, {"kind": "Conformance", "name": "Strideable", "printedName": "Strideable", "children": [{"kind": "TypeWitness", "name": "Stride", "printedName": "Stride", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:Sx", "mangledName": "$sSx"}, {"kind": "Conformance", "name": "AdditiveArithmetic", "printedName": "AdditiveArithmetic", "usr": "s:s18AdditiveArithmeticP", "mangledName": "$ss18AdditiveArithmeticP"}, {"kind": "Conformance", "name": "ExpressibleByIntegerLiteral", "printedName": "ExpressibleByIntegerLiteral", "children": [{"kind": "TypeWitness", "name": "IntegerLiteralType", "printedName": "IntegerLiteralType", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:s27ExpressibleByIntegerLiteralP", "mangledName": "$ss27ExpressibleByIntegerLiteralP"}, {"kind": "Conformance", "name": "Comparable", "printedName": "Comparable", "usr": "s:SL", "mangledName": "$sSL"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "CodingKeyRepresentable", "printedName": "CodingKeyRepresentable", "usr": "s:s22CodingKeyRepresentableP", "mangledName": "$ss22CodingKeyRepresentableP"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "_CustomPlaygroundQuickLookable", "printedName": "_CustomPlaygroundQuickLookable", "usr": "s:s30_CustomPlaygroundQuickLookableP", "mangledName": "$ss30_CustomPlaygroundQuickLookableP"}, {"kind": "Conformance", "name": "MirrorPath", "printedName": "MirrorPath", "usr": "s:s10MirrorPathP", "mangledName": "$ss10MirrorPathP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "_HasCustomAnyHashableRepresentation", "printedName": "_HasCustomAnyHashableRepresentation", "usr": "s:s35_HasCustomAnyHashableRepresentationP", "mangledName": "$ss35_HasCustomAnyHashableRepresentationP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "SIMDScalar", "printedName": "SIMDScalar", "children": [{"kind": "TypeWitness", "name": "SIMDMaskScalar", "printedName": "SIMDMaskScalar", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}, {"kind": "TypeWitness", "name": "SIMD2Storage", "printedName": "SIMD2Storage", "children": [{"kind": "TypeNominal", "name": "SIMD2Storage", "printedName": "Swift.Int.SIMD2Storage", "usr": "s:Si12SIMD2StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD4Storage", "printedName": "SIMD4Storage", "children": [{"kind": "TypeNominal", "name": "SIMD4Storage", "printedName": "Swift.Int.SIMD4Storage", "usr": "s:Si12SIMD4StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD8Storage", "printedName": "SIMD8Storage", "children": [{"kind": "TypeNominal", "name": "SIMD8Storage", "printedName": "Swift.Int.SIMD8Storage", "usr": "s:Si12SIMD8StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD16Storage", "printedName": "SIMD16Storage", "children": [{"kind": "TypeNominal", "name": "SIMD16Storage", "printedName": "Swift.Int.SIMD16Storage", "usr": "s:Si13SIMD16StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD32Storage", "printedName": "SIMD32Storage", "children": [{"kind": "TypeNominal", "name": "SIMD32Storage", "printedName": "Swift.Int.SIMD32Storage", "usr": "s:Si13SIMD32StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD64Storage", "printedName": "SIMD64Storage", "children": [{"kind": "TypeNominal", "name": "SIMD64Storage", "printedName": "Swift.Int.SIMD64Storage", "usr": "s:Si13SIMD64StorageV"}]}], "usr": "s:s10SIMDScalarP", "mangledName": "$ss10SIMDScalarP"}, {"kind": "Conformance", "name": "BitwiseCopyable", "printedName": "BitwiseCopyable", "usr": "s:s15BitwiseCopyableP", "mangledName": "$ss15BitwiseCopyableP"}, {"kind": "Conformance", "name": "_FormatSpecifiable", "printedName": "_FormatSpecifiable", "children": [{"kind": "TypeWitness", "name": "_Arg", "printedName": "_Arg", "children": [{"kind": "TypeNominal", "name": "Int64", "printedName": "Swift.Int64", "usr": "s:s5Int64V"}]}], "usr": "s:10Foundation18_FormatSpecifiableP", "mangledName": "$s10Foundation18_FormatSpecifiableP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSNumber", "printedName": "Foundation.NSNumber", "usr": "c:objc(cs)NSNumber"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "_FormatSpecifiable", "printedName": "_FormatSpecifiable", "children": [{"kind": "TypeWitness", "name": "_Arg", "printedName": "_Arg", "children": [{"kind": "TypeNominal", "name": "Int64", "printedName": "Swift.Int64", "usr": "s:s5Int64V"}]}], "usr": "s:7SwiftUI18_FormatSpecifiableP", "mangledName": "$s7SwiftUI18_FormatSpecifiableP"}]}, {"kind": "TypeDecl", "name": "String", "printedName": "String", "declKind": "Struct", "usr": "s:SS", "mangledName": "$sSS", "moduleName": "Swift", "declAttributes": ["<PERSON>agerMove", "Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "CodingKeyRepresentable", "printedName": "CodingKeyRepresentable", "usr": "s:s22CodingKeyRepresentableP", "mangledName": "$ss22CodingKeyRepresentableP"}, {"kind": "Conformance", "name": "_HasContiguousBytes", "printedName": "_HasContiguousBytes", "usr": "s:s19_HasContiguousBytesP", "mangledName": "$ss19_HasContiguousBytesP"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "_CustomPlaygroundQuickLookable", "printedName": "_CustomPlaygroundQuickLookable", "usr": "s:s30_CustomPlaygroundQuickLookableP", "mangledName": "$ss30_CustomPlaygroundQuickLookableP"}, {"kind": "Conformance", "name": "TextOutputStream", "printedName": "TextOutputStream", "usr": "s:s16TextOutputStreamP", "mangledName": "$ss16TextOutputStreamP"}, {"kind": "Conformance", "name": "TextOutputStreamable", "printedName": "TextOutputStreamable", "usr": "s:s20TextOutputStreamableP", "mangledName": "$ss20TextOutputStreamableP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinUnicodeScalarLiteral", "printedName": "_ExpressibleByBuiltinUnicodeScalarLiteral", "usr": "s:s41_ExpressibleByBuiltinUnicodeScalarLiteralP", "mangledName": "$ss41_ExpressibleByBuiltinUnicodeScalarLiteralP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinExtendedGraphemeClusterLiteral", "printedName": "_ExpressibleByBuiltinExtendedGraphemeClusterLiteral", "usr": "s:s51_ExpressibleByBuiltinExtendedGraphemeClusterLiteralP", "mangledName": "$ss51_ExpressibleByBuiltinExtendedGraphemeClusterLiteralP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinStringLiteral", "printedName": "_ExpressibleByBuiltinStringLiteral", "usr": "s:s34_ExpressibleByBuiltinStringLiteralP", "mangledName": "$ss34_ExpressibleByBuiltinStringLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByStringLiteral", "printedName": "ExpressibleByStringLiteral", "children": [{"kind": "TypeWitness", "name": "StringLiteralType", "printedName": "StringLiteralType", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}], "usr": "s:s26ExpressibleByStringLiteralP", "mangledName": "$ss26ExpressibleByStringLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByExtendedGraphemeClusterLiteral", "printedName": "ExpressibleByExtendedGraphemeClusterLiteral", "children": [{"kind": "TypeWitness", "name": "ExtendedGraphemeClusterLiteralType", "printedName": "ExtendedGraphemeClusterLiteralType", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}], "usr": "s:s43ExpressibleByExtendedGraphemeClusterLiteralP", "mangledName": "$ss43ExpressibleByExtendedGraphemeClusterLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByUnicodeScalarLiteral", "printedName": "ExpressibleByUnicodeScalarLiteral", "children": [{"kind": "TypeWitness", "name": "UnicodeScalarLiteralType", "printedName": "UnicodeScalarLiteralType", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}], "usr": "s:s33ExpressibleByUnicodeScalarLiteralP", "mangledName": "$ss33ExpressibleByUnicodeScalarLiteralP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "BidirectionalCollection", "printedName": "BidirectionalCollection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "Character", "printedName": "Swift.Character", "usr": "s:SJ"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Index", "printedName": "Swift.String.Index", "usr": "s:SS5IndexV"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "Substring", "printedName": "Swift.Substring", "usr": "s:Ss"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "DefaultIndices", "printedName": "Swift.DefaultIndices<Swift.String>", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:SI"}]}], "usr": "s:SK", "mangledName": "$sSK"}, {"kind": "Conformance", "name": "Collection", "printedName": "Collection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "Character", "printedName": "Swift.Character", "usr": "s:SJ"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Index", "printedName": "Swift.String.Index", "usr": "s:SS5IndexV"}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "Iterator", "printedName": "Swift.String.Iterator", "usr": "s:SS8IteratorV"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "Substring", "printedName": "Swift.Substring", "usr": "s:Ss"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "DefaultIndices", "printedName": "Swift.DefaultIndices<Swift.String>", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:SI"}]}], "usr": "s:Sl", "mangledName": "$sSl"}, {"kind": "Conformance", "name": "Sequence", "printedName": "Sequence", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "Character", "printedName": "Swift.Character", "usr": "s:SJ"}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "Iterator", "printedName": "Swift.String.Iterator", "usr": "s:SS8IteratorV"}]}], "usr": "s:ST", "mangledName": "$sST"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "Comparable", "printedName": "Comparable", "usr": "s:SL", "mangledName": "$sSL"}, {"kind": "Conformance", "name": "StringProtocol", "printedName": "StringProtocol", "children": [{"kind": "TypeWitness", "name": "UTF8View", "printedName": "UTF8View", "children": [{"kind": "TypeNominal", "name": "UTF8View", "printedName": "Swift.String.UTF8View", "usr": "s:SS8UTF8ViewV"}]}, {"kind": "TypeWitness", "name": "UTF16View", "printedName": "UTF16View", "children": [{"kind": "TypeNominal", "name": "UTF16View", "printedName": "Swift.String.UTF16View", "usr": "s:SS9UTF16ViewV"}]}, {"kind": "TypeWitness", "name": "UnicodeScalarView", "printedName": "UnicodeScalarView", "children": [{"kind": "TypeNominal", "name": "UnicodeScalarView", "printedName": "Swift.String.UnicodeScalarView", "usr": "s:SS17UnicodeScalarViewV"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "Substring", "printedName": "Swift.Substring", "usr": "s:Ss"}]}], "usr": "s:<PERSON>y", "mangledName": "$sSy"}, {"kind": "Conformance", "name": "ExpressibleByStringInterpolation", "printedName": "ExpressibleByStringInterpolation", "children": [{"kind": "TypeWitness", "name": "StringInterpolation", "printedName": "StringInterpolation", "children": [{"kind": "TypeNominal", "name": "DefaultStringInterpolation", "printedName": "Swift.DefaultStringInterpolation", "usr": "s:s26DefaultStringInterpolationV"}]}], "usr": "s:s32ExpressibleByStringInterpolationP", "mangledName": "$ss32ExpressibleByStringInterpolationP"}, {"kind": "Conformance", "name": "LosslessStringConvertible", "printedName": "LosslessStringConvertible", "usr": "s:s25LosslessStringConvertibleP", "mangledName": "$ss25LosslessStringConvertibleP"}, {"kind": "Conformance", "name": "RangeReplaceableCollection", "printedName": "RangeReplaceableCollection", "children": [{"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "Substring", "printedName": "Swift.Substring", "usr": "s:Ss"}]}], "usr": "s:Sm", "mangledName": "$sSm"}, {"kind": "Conformance", "name": "MirrorPath", "printedName": "MirrorPath", "usr": "s:s10MirrorPathP", "mangledName": "$ss10MirrorPathP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSString", "printedName": "Foundation.NSString", "usr": "c:objc(cs)NSString"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "Transferable", "printedName": "Transferable", "children": [{"kind": "TypeWitness", "name": "Representation", "printedName": "Representation", "children": [{"kind": "TypeNominal", "name": "OpaqueTypeArchetype", "printedName": "some CoreTransferable.TransferRepresentation", "children": [{"kind": "TypeNominal", "name": "TransferRepresentation", "printedName": "CoreTransferable.TransferRepresentation", "usr": "s:16CoreTransferable22TransferRepresentationP"}]}]}], "usr": "s:16CoreTransferable0B0P", "mangledName": "$s16CoreTransferable0B0P"}]}, {"kind": "TypeDecl", "name": "Double", "printedName": "Double", "declKind": "Struct", "usr": "s:Sd", "mangledName": "$sSd", "moduleName": "Swift", "declAttributes": ["Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "_CustomPlaygroundQuickLookable", "printedName": "_CustomPlaygroundQuickLookable", "usr": "s:s30_CustomPlaygroundQuickLookableP", "mangledName": "$ss30_CustomPlaygroundQuickLookableP"}, {"kind": "Conformance", "name": "_CVarArgPassedAsDouble", "printedName": "_CVarArgPassedAsDouble", "usr": "s:s22_CVarArgPassedAsDoubleP", "mangledName": "$ss22_CVarArgPassedAsDoubleP"}, {"kind": "Conformance", "name": "_CVarArgAligned", "printedName": "_CVarArgAligned", "usr": "s:s15_CVarArgAlignedP", "mangledName": "$ss15_CVarArgAlignedP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "LosslessStringConvertible", "printedName": "LosslessStringConvertible", "usr": "s:s25LosslessStringConvertibleP", "mangledName": "$ss25LosslessStringConvertibleP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "TextOutputStreamable", "printedName": "TextOutputStreamable", "usr": "s:s20TextOutputStreamableP", "mangledName": "$ss20TextOutputStreamableP"}, {"kind": "Conformance", "name": "BinaryFloatingPoint", "printedName": "BinaryFloatingPoint", "children": [{"kind": "TypeWitness", "name": "RawSignificand", "printedName": "RawSignificand", "children": [{"kind": "TypeNominal", "name": "UInt64", "printedName": "Swift.UInt64", "usr": "s:s6UInt64V"}]}, {"kind": "TypeWitness", "name": "RawExponent", "printedName": "RawExponent", "children": [{"kind": "TypeNominal", "name": "UInt", "printedName": "Swift.UInt", "usr": "s:<PERSON>"}]}], "usr": "s:SB", "mangledName": "$sSB"}, {"kind": "Conformance", "name": "ExpressibleByFloatLiteral", "printedName": "ExpressibleByFloatLiteral", "children": [{"kind": "TypeWitness", "name": "FloatLiteralType", "printedName": "FloatLiteralType", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:s25ExpressibleByFloatLiteralP", "mangledName": "$ss25ExpressibleByFloatLiteralP"}, {"kind": "Conformance", "name": "FloatingPoint", "printedName": "FloatingPoint", "children": [{"kind": "TypeWitness", "name": "Exponent", "printedName": "Exponent", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SF", "mangledName": "$sSF"}, {"kind": "Conformance", "name": "SignedNumeric", "printedName": "SignedNumeric", "usr": "s:s13SignedNumericP", "mangledName": "$ss13SignedNumericP"}, {"kind": "Conformance", "name": "Numeric", "printedName": "Numeric", "children": [{"kind": "TypeWitness", "name": "Magnitude", "printedName": "Magnitude", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:Sj", "mangledName": "$sSj"}, {"kind": "Conformance", "name": "AdditiveArithmetic", "printedName": "AdditiveArithmetic", "usr": "s:s18AdditiveArithmeticP", "mangledName": "$ss18AdditiveArithmeticP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinIntegerLiteral", "printedName": "_ExpressibleByBuiltinIntegerLiteral", "usr": "s:s35_ExpressibleByBuiltinIntegerLiteralP", "mangledName": "$ss35_ExpressibleByBuiltinIntegerLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByIntegerLiteral", "printedName": "ExpressibleByIntegerLiteral", "children": [{"kind": "TypeWitness", "name": "IntegerLiteralType", "printedName": "IntegerLiteralType", "children": [{"kind": "TypeNominal", "name": "Int64", "printedName": "Swift.Int64", "usr": "s:s5Int64V"}]}], "usr": "s:s27ExpressibleByIntegerLiteralP", "mangledName": "$ss27ExpressibleByIntegerLiteralP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinFloatLiteral", "printedName": "_ExpressibleByBuiltinFloatLiteral", "usr": "s:s33_ExpressibleByBuiltinFloatLiteralP", "mangledName": "$ss33_ExpressibleByBuiltinFloatLiteralP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "_HasCustomAnyHashableRepresentation", "printedName": "_HasCustomAnyHashableRepresentation", "usr": "s:s35_HasCustomAnyHashableRepresentationP", "mangledName": "$ss35_HasCustomAnyHashableRepresentationP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Strideable", "printedName": "Strideable", "children": [{"kind": "TypeWitness", "name": "Stride", "printedName": "Stride", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:Sx", "mangledName": "$sSx"}, {"kind": "Conformance", "name": "Comparable", "printedName": "Comparable", "usr": "s:SL", "mangledName": "$sSL"}, {"kind": "Conformance", "name": "SIMDScalar", "printedName": "SIMDScalar", "children": [{"kind": "TypeWitness", "name": "SIMDMaskScalar", "printedName": "SIMDMaskScalar", "children": [{"kind": "TypeNominal", "name": "Int64", "printedName": "Swift.Int64", "usr": "s:s5Int64V"}]}, {"kind": "TypeWitness", "name": "SIMD2Storage", "printedName": "SIMD2Storage", "children": [{"kind": "TypeNominal", "name": "SIMD2Storage", "printedName": "Swift.Double.SIMD2Storage", "usr": "s:Sd12SIMD2StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD4Storage", "printedName": "SIMD4Storage", "children": [{"kind": "TypeNominal", "name": "SIMD4Storage", "printedName": "Swift.Double.SIMD4Storage", "usr": "s:Sd12SIMD4StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD8Storage", "printedName": "SIMD8Storage", "children": [{"kind": "TypeNominal", "name": "SIMD8Storage", "printedName": "Swift.Double.SIMD8Storage", "usr": "s:Sd12SIMD8StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD16Storage", "printedName": "SIMD16Storage", "children": [{"kind": "TypeNominal", "name": "SIMD16Storage", "printedName": "Swift.Double.SIMD16Storage", "usr": "s:Sd13SIMD16StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD32Storage", "printedName": "SIMD32Storage", "children": [{"kind": "TypeNominal", "name": "SIMD32Storage", "printedName": "Swift.Double.SIMD32Storage", "usr": "s:Sd13SIMD32StorageV"}]}, {"kind": "TypeWitness", "name": "SIMD64Storage", "printedName": "SIMD64Storage", "children": [{"kind": "TypeNominal", "name": "SIMD64Storage", "printedName": "Swift.Double.SIMD64Storage", "usr": "s:Sd13SIMD64StorageV"}]}], "usr": "s:s10SIMDScalarP", "mangledName": "$ss10SIMDScalarP"}, {"kind": "Conformance", "name": "BitwiseCopyable", "printedName": "BitwiseCopyable", "usr": "s:s15BitwiseCopyableP", "mangledName": "$ss15BitwiseCopyableP"}, {"kind": "Conformance", "name": "_FormatSpecifiable", "printedName": "_FormatSpecifiable", "children": [{"kind": "TypeWitness", "name": "_Arg", "printedName": "_Arg", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:10Foundation18_FormatSpecifiableP", "mangledName": "$s10Foundation18_FormatSpecifiableP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSNumber", "printedName": "Foundation.NSNumber", "usr": "c:objc(cs)NSNumber"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "_FormatSpecifiable", "printedName": "_FormatSpecifiable", "children": [{"kind": "TypeWitness", "name": "_Arg", "printedName": "_Arg", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:7SwiftUI18_FormatSpecifiableP", "mangledName": "$s7SwiftUI18_FormatSpecifiableP"}, {"kind": "Conformance", "name": "VectorArithmetic", "printedName": "VectorArithmetic", "usr": "s:7SwiftUI16VectorArithmeticP", "mangledName": "$s7SwiftUI16VectorArithmeticP"}, {"kind": "Conformance", "name": "Animatable", "printedName": "Animatable", "children": [{"kind": "TypeWitness", "name": "AnimatableData", "printedName": "AnimatableData", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:7SwiftUI10AnimatableP", "mangledName": "$s7SwiftUI10AnimatableP"}]}, {"kind": "TypeDecl", "name": "Decimal", "printedName": "Decimal", "declKind": "Struct", "usr": "c:@SA@NSDecimal", "moduleName": "Foundation", "declAttributes": ["SynthesizedProtocol", "NonSendable", "Sendable"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "SignedNumeric", "printedName": "SignedNumeric", "usr": "s:s13SignedNumericP", "mangledName": "$ss13SignedNumericP"}, {"kind": "Conformance", "name": "Numeric", "printedName": "Numeric", "children": [{"kind": "TypeWitness", "name": "Magnitude", "printedName": "Magnitude", "children": [{"kind": "TypeNominal", "name": "Decimal", "printedName": "Foundation.Decimal", "usr": "c:@SA@NSDecimal"}]}], "usr": "s:Sj", "mangledName": "$sSj"}, {"kind": "Conformance", "name": "AdditiveArithmetic", "printedName": "AdditiveArithmetic", "usr": "s:s18AdditiveArithmeticP", "mangledName": "$ss18AdditiveArithmeticP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSDecimalNumber", "printedName": "Foundation.NSDecimalNumber", "usr": "c:objc(cs)NSDecimalNumber"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "ExpressibleByFloatLiteral", "printedName": "ExpressibleByFloatLiteral", "children": [{"kind": "TypeWitness", "name": "FloatLiteralType", "printedName": "FloatLiteralType", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}]}], "usr": "s:s25ExpressibleByFloatLiteralP", "mangledName": "$ss25ExpressibleByFloatLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByIntegerLiteral", "printedName": "ExpressibleByIntegerLiteral", "children": [{"kind": "TypeWitness", "name": "IntegerLiteralType", "printedName": "IntegerLiteralType", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:s27ExpressibleByIntegerLiteralP", "mangledName": "$ss27ExpressibleByIntegerLiteralP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Comparable", "printedName": "Comparable", "usr": "s:SL", "mangledName": "$sSL"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "Strideable", "printedName": "Strideable", "children": [{"kind": "TypeWitness", "name": "Stride", "printedName": "Stride", "children": [{"kind": "TypeNominal", "name": "Decimal", "printedName": "Foundation.Decimal", "usr": "c:@SA@NSDecimal"}]}], "usr": "s:Sx", "mangledName": "$sSx"}]}, {"kind": "TypeDecl", "name": "Bool", "printedName": "Bool", "declKind": "Struct", "usr": "s:Sb", "mangledName": "$sSb", "moduleName": "Swift", "declAttributes": ["Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "_ExpressibleByBuiltinBooleanLiteral", "printedName": "_ExpressibleByBuiltinBooleanLiteral", "usr": "s:s35_ExpressibleByBuiltinBooleanLiteralP", "mangledName": "$ss35_ExpressibleByBuiltinBooleanLiteralP"}, {"kind": "Conformance", "name": "ExpressibleByBooleanLiteral", "printedName": "ExpressibleByBooleanLiteral", "children": [{"kind": "TypeWitness", "name": "BooleanLiteralType", "printedName": "BooleanLiteralType", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}]}], "usr": "s:s27ExpressibleByBooleanLiteralP", "mangledName": "$ss27ExpressibleByBooleanLiteralP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "LosslessStringConvertible", "printedName": "LosslessStringConvertible", "usr": "s:s25LosslessStringConvertibleP", "mangledName": "$ss25LosslessStringConvertibleP"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "_CustomPlaygroundQuickLookable", "printedName": "_CustomPlaygroundQuickLookable", "usr": "s:s30_CustomPlaygroundQuickLookableP", "mangledName": "$ss30_CustomPlaygroundQuickLookableP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "BitwiseCopyable", "printedName": "BitwiseCopyable", "usr": "s:s15BitwiseCopyableP", "mangledName": "$ss15BitwiseCopyableP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSNumber", "printedName": "Foundation.NSNumber", "usr": "c:objc(cs)NSNumber"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}]}, {"kind": "TypeDecl", "name": "Optional", "printedName": "Optional", "declKind": "Enum", "usr": "s:Sq", "mangledName": "$sSq", "moduleName": "Swift", "genericSig": "<τ_0_0 where τ_0_0 : ~Copyable>", "sugared_genericSig": "<Wrapped where Wrapped : ~Copyable>", "declAttributes": ["Frozen"], "isExternal": true, "isEnumExhaustive": true, "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "BitwiseCopyable", "printedName": "BitwiseCopyable", "usr": "s:s15BitwiseCopyableP", "mangledName": "$ss15BitwiseCopyableP"}, {"kind": "Conformance", "name": "ExpressibleByNilLiteral", "printedName": "ExpressibleByNilLiteral", "usr": "s:s23ExpressibleByNilLiteralP", "mangledName": "$ss23ExpressibleByNilLiteralP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "AnyObject"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "EncodableWithConfiguration", "printedName": "EncodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "EncodingConfiguration", "printedName": "EncodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.EncodingConfiguration"}]}], "usr": "s:10Foundation26EncodableWithConfigurationP", "mangledName": "$s10Foundation26EncodableWithConfigurationP"}, {"kind": "Conformance", "name": "DecodableWithConfiguration", "printedName": "DecodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "DecodingConfiguration", "printedName": "DecodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.DecodingConfiguration"}]}], "usr": "s:10Foundation26DecodableWithConfigurationP", "mangledName": "$s10Foundation26DecodableWithConfigurationP"}, {"kind": "Conformance", "name": "Gesture", "printedName": "Gesture", "children": [{"kind": "TypeWitness", "name": "Value", "printedName": "Value", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.Value"}]}, {"kind": "TypeWitness", "name": "Body", "printedName": "Body", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI7GestureP", "mangledName": "$s7SwiftUI7GestureP"}, {"kind": "Conformance", "name": "View", "printedName": "View", "children": [{"kind": "TypeWitness", "name": "Body", "printedName": "Body", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI4ViewP", "mangledName": "$s7SwiftUI4ViewP"}, {"kind": "Conformance", "name": "TableColumnContent", "printedName": "TableColumnContent", "children": [{"kind": "TypeWitness", "name": "TableRowValue", "printedName": "TableRowValue", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.TableRowValue"}]}, {"kind": "TypeWitness", "name": "TableColumnSortComparator", "printedName": "TableColumnSortComparator", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.TableColumnSortComparator"}]}, {"kind": "TypeWitness", "name": "TableColumnBody", "printedName": "TableColumnBody", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI18TableColumnContentP", "mangledName": "$s7SwiftUI18TableColumnContentP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"kind": "TypeWitness", "name": "TabValue", "printedName": "TabValue", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.TabV<PERSON>ue"}]}, {"kind": "TypeWitness", "name": "_IdentifiedView", "printedName": "_IdentifiedView", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "τ_0_0._IdentifiedView?", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0._IdentifiedView"}], "usr": "s:Sq"}]}, {"kind": "TypeWitness", "name": "Body", "printedName": "Body", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "τ_0_0?", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:Sq"}]}], "usr": "s:7SwiftUI10TabContentP", "mangledName": "$s7SwiftUI10TabContentP"}, {"kind": "Conformance", "name": "TableRowContent", "printedName": "TableRowContent", "children": [{"kind": "TypeWitness", "name": "TableRowValue", "printedName": "TableRowValue", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.TableRowValue"}]}, {"kind": "TypeWitness", "name": "TableRowBody", "printedName": "TableRowBody", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI15TableRowContentP", "mangledName": "$s7SwiftUI15TableRowContentP"}, {"kind": "Conformance", "name": "Commands", "printedName": "Commands", "children": [{"kind": "TypeWitness", "name": "Body", "printedName": "Body", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI8CommandsP", "mangledName": "$s7SwiftUI8CommandsP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"kind": "TypeWitness", "name": "Body", "printedName": "Body", "children": [{"kind": "TypeNominal", "name": "Never", "printedName": "Swift.Never", "usr": "s:s5NeverO"}]}], "usr": "s:7SwiftUI14ToolbarContentP", "mangledName": "$s7SwiftUI14ToolbarContentP"}, {"kind": "Conformance", "name": "CustomizableToolbarContent", "printedName": "CustomizableToolbarContent", "usr": "s:7SwiftUI26CustomizableToolbarContentP", "mangledName": "$s7SwiftUI26CustomizableToolbarContentP"}]}, {"kind": "TypeDecl", "name": "Array", "printedName": "Array", "declKind": "Struct", "usr": "s:Sa", "mangledName": "$sSa", "moduleName": "Swift", "genericSig": "<τ_0_0>", "sugared_genericSig": "<Element>", "declAttributes": ["<PERSON>agerMove", "Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "_DestructorSafeContainer", "printedName": "_DestructorSafeContainer", "usr": "s:s24_DestructorSafeContainerP", "mangledName": "$ss24_DestructorSafeContainerP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "JSONRoot", "printedName": "JSONRoot", "usr": "s:13EkycFramework8JSONRootP", "mangledName": "$s13EkycFramework8JSONRootP"}, {"kind": "Conformance", "name": "_ArrayProtocol", "printedName": "_ArrayProtocol", "children": [{"kind": "TypeWitness", "name": "_<PERSON><PERSON>er", "printedName": "_<PERSON><PERSON>er", "children": [{"kind": "TypeNominal", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printedName": "<PERSON>._<PERSON><PERSON>y<PERSON>uffer<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s12_ArrayBufferV"}]}], "usr": "s:s14_ArrayProtocolP", "mangledName": "$ss14_ArrayProtocolP"}, {"kind": "Conformance", "name": "RandomAccessCollection", "printedName": "RandomAccessCollection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "ArraySlice", "printedName": "Swift.ArraySlice<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s10ArraySliceV"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "Range", "printedName": "Swift.Range<Swift.Int>", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "usr": "s:Sn"}]}], "usr": "s:Sk", "mangledName": "$sSk"}, {"kind": "Conformance", "name": "MutableCollection", "printedName": "MutableCollection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "ArraySlice", "printedName": "Swift.ArraySlice<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s10ArraySliceV"}]}], "usr": "s:SM", "mangledName": "$sSM"}, {"kind": "Conformance", "name": "BidirectionalCollection", "printedName": "BidirectionalCollection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "ArraySlice", "printedName": "Swift.ArraySlice<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s10ArraySliceV"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "Range", "printedName": "Swift.Range<Swift.Int>", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "usr": "s:Sn"}]}], "usr": "s:SK", "mangledName": "$sSK"}, {"kind": "Conformance", "name": "Collection", "printedName": "Collection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "IndexingIterator", "printedName": "Swift.IndexingIterator<[τ_0_0]>", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[τ_0_0]", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:Sa"}], "usr": "s:s16IndexingIteratorV"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "ArraySlice", "printedName": "Swift.ArraySlice<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s10ArraySliceV"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "Range", "printedName": "Swift.Range<Swift.Int>", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "usr": "s:Sn"}]}], "usr": "s:Sl", "mangledName": "$sSl"}, {"kind": "Conformance", "name": "Sequence", "printedName": "Sequence", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "IndexingIterator", "printedName": "Swift.IndexingIterator<[τ_0_0]>", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[τ_0_0]", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:Sa"}], "usr": "s:s16IndexingIteratorV"}]}], "usr": "s:ST", "mangledName": "$sST"}, {"kind": "Conformance", "name": "ExpressibleByArrayLiteral", "printedName": "ExpressibleByArrayLiteral", "children": [{"kind": "TypeWitness", "name": "ArrayLiteralElement", "printedName": "ArrayLiteralElement", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}], "usr": "s:s25ExpressibleByArrayLiteralP", "mangledName": "$ss25ExpressibleByArrayLiteralP"}, {"kind": "Conformance", "name": "RangeReplaceableCollection", "printedName": "RangeReplaceableCollection", "children": [{"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "ArraySlice", "printedName": "Swift.ArraySlice<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:s10ArraySliceV"}]}], "usr": "s:Sm", "mangledName": "$sSm"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "_HasCustomAnyHashableRepresentation", "printedName": "_HasCustomAnyHashableRepresentation", "usr": "s:s35_HasCustomAnyHashableRepresentationP", "mangledName": "$ss35_HasCustomAnyHashableRepresentationP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "_HasContiguousBytes", "printedName": "_HasContiguousBytes", "usr": "s:s19_HasContiguousBytesP", "mangledName": "$ss19_HasContiguousBytesP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSA<PERSON>y", "printedName": "Foundation.NSArray", "usr": "c:objc(cs)NSArray"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "DataProtocol", "printedName": "DataProtocol", "children": [{"kind": "TypeWitness", "name": "Regions", "printedName": "Regions", "children": [{"kind": "TypeNominal", "name": "CollectionOfOne", "printedName": "Swift.CollectionOfOne<[Swift.UInt8]>", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[Swift.UInt8]", "children": [{"kind": "TypeNominal", "name": "UInt8", "printedName": "Swift.UInt8", "usr": "s:s5UInt8V"}], "usr": "s:Sa"}], "usr": "s:s15CollectionOfOneV"}]}], "usr": "s:10Foundation12DataProtocolP", "mangledName": "$s10Foundation12DataProtocolP"}, {"kind": "Conformance", "name": "MutableDataProtocol", "printedName": "MutableDataProtocol", "usr": "s:10Foundation19MutableDataProtocolP", "mangledName": "$s10Foundation19MutableDataProtocolP"}, {"kind": "Conformance", "name": "ContiguousBytes", "printedName": "ContiguousBytes", "usr": "s:10Foundation15ContiguousBytesP", "mangledName": "$s10Foundation15ContiguousBytesP"}, {"kind": "Conformance", "name": "EncodableWithConfiguration", "printedName": "EncodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "EncodingConfiguration", "printedName": "EncodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.EncodingConfiguration"}]}], "usr": "s:10Foundation26EncodableWithConfigurationP", "mangledName": "$s10Foundation26EncodableWithConfigurationP"}, {"kind": "Conformance", "name": "DecodableWithConfiguration", "printedName": "DecodableWithConfiguration", "children": [{"kind": "TypeWitness", "name": "DecodingConfiguration", "printedName": "DecodingConfiguration", "children": [{"kind": "TypeNominal", "name": "DependentMember", "printedName": "τ_0_0.DecodingConfiguration"}]}], "usr": "s:10Foundation26DecodableWithConfigurationP", "mangledName": "$s10Foundation26DecodableWithConfigurationP"}]}, {"kind": "TypeDecl", "name": "Dictionary", "printedName": "Dictionary", "declKind": "Struct", "usr": "s:SD", "mangledName": "$sSD", "moduleName": "Swift", "genericSig": "<τ_0_0, τ_0_1 where τ_0_0 : <PERSON><PERSON>>", "sugared_genericSig": "<Key, Value where Key : <PERSON><PERSON>>", "declAttributes": ["<PERSON>agerMove", "Frozen"], "isExternal": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "JSONValue", "printedName": "JSONValue", "usr": "s:13EkycFramework9JSONValueP", "mangledName": "$s13EkycFramework9JSONValueP"}, {"kind": "Conformance", "name": "JSONRoot", "printedName": "JSONRoot", "usr": "s:13EkycFramework8JSONRootP", "mangledName": "$s13EkycFramework8JSONRootP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "Encodable", "printedName": "Encodable", "usr": "s:SE", "mangledName": "$sSE"}, {"kind": "Conformance", "name": "Decodable", "printedName": "Decodable", "usr": "s:Se", "mangledName": "$sSe"}, {"kind": "Conformance", "name": "Sequence", "printedName": "Sequence", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(key: τ_0_0, value: τ_0_1)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_1"}]}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "Iterator", "printedName": "Swift.Dictionary<τ_0_0, τ_0_1>.Iterator", "usr": "s:SD8IteratorV"}]}], "usr": "s:ST", "mangledName": "$sST"}, {"kind": "Conformance", "name": "Collection", "printedName": "Collection", "children": [{"kind": "TypeWitness", "name": "Element", "printedName": "Element", "children": [{"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(key: τ_0_0, value: τ_0_1)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_1"}]}]}, {"kind": "TypeWitness", "name": "Index", "printedName": "Index", "children": [{"kind": "TypeNominal", "name": "Index", "printedName": "Swift.Dictionary<τ_0_0, τ_0_1>.Index", "usr": "s:SD5IndexV"}]}, {"kind": "TypeWitness", "name": "Iterator", "printedName": "Iterator", "children": [{"kind": "TypeNominal", "name": "Iterator", "printedName": "Swift.Dictionary<τ_0_0, τ_0_1>.Iterator", "usr": "s:SD8IteratorV"}]}, {"kind": "TypeWitness", "name": "SubSequence", "printedName": "SubSequence", "children": [{"kind": "TypeNominal", "name": "Slice", "printedName": "Swift.Slice<[τ_0_0 : τ_0_1]>", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[τ_0_0 : τ_0_1]", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_1"}], "usr": "s:SD"}], "usr": "s:s5SliceV"}]}, {"kind": "TypeWitness", "name": "Indices", "printedName": "Indices", "children": [{"kind": "TypeNominal", "name": "DefaultIndices", "printedName": "Swift.DefaultIndices<[τ_0_0 : τ_0_1]>", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[τ_0_0 : τ_0_1]", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_1"}], "usr": "s:SD"}], "usr": "s:SI"}]}], "usr": "s:Sl", "mangledName": "$sSl"}, {"kind": "Conformance", "name": "ExpressibleByDictionaryLiteral", "printedName": "ExpressibleByDictionaryLiteral", "children": [{"kind": "TypeWitness", "name": "Key", "printedName": "Key", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeWitness", "name": "Value", "printedName": "Value", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_1"}]}], "usr": "s:s30ExpressibleByDictionaryLiteralP", "mangledName": "$ss30ExpressibleByDictionaryLiteralP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "_HasCustomAnyHashableRepresentation", "printedName": "_HasCustomAnyHashableRepresentation", "usr": "s:s35_HasCustomAnyHashableRepresentationP", "mangledName": "$ss35_HasCustomAnyHashableRepresentationP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "CustomReflectable", "printedName": "CustomReflectable", "usr": "s:s17CustomReflectableP", "mangledName": "$ss17CustomReflectableP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "_ObjectiveCBridgeable", "printedName": "_ObjectiveCBridgeable", "children": [{"kind": "TypeWitness", "name": "_ObjectiveCType", "printedName": "_ObjectiveCType", "children": [{"kind": "TypeNominal", "name": "NSDictionary", "printedName": "Foundation.NSDictionary", "usr": "c:objc(cs)NSDictionary"}]}], "usr": "s:s21_ObjectiveCBridgeableP", "mangledName": "$ss21_ObjectiveCBridgeableP"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "Array", "offset": 3107, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "Array", "offset": 3141, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3174, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3249, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3324, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3393, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3537, "length": 19, "value": "\"as-data-completed\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3578, "length": 17, "value": "\"user-select-idp\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3618, "length": 13, "value": "\"idp-pending\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3653, "length": 22, "value": "\"idp-confirmed-accept\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3696, "length": 13, "value": "\"idp-errored\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3729, "length": 12, "value": "\"as-errored\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3764, "length": 21, "value": "\"idp-pending-timeout\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3814, "length": 30, "value": "\"idp-confirmed-accept-timeout\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3875, "length": 23, "value": "\"ndid-connection-fail \""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationSelectIdp/NdidVerificationSelectIdpViewController.swift", "kind": "StringLiteral", "offset": 3920, "length": 22, "value": "\"idp-confirmed-reject\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 530, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "IntegerLiteral", "offset": 616, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 820, "length": 9, "value": "\"iPod5,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 846, "length": 29, "value": "\"iPod touch (5th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 887, "length": 9, "value": "\"iPod7,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 913, "length": 29, "value": "\"iPod touch (6th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 954, "length": 9, "value": "\"iPod9,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 980, "length": 29, "value": "\"iPod touch (7th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1021, "length": 11, "value": "\"iPhone3,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1034, "length": 11, "value": "\"iPhone3,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1047, "length": 11, "value": "\"iPhone3,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1075, "length": 10, "value": "\"iPhone 4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1097, "length": 11, "value": "\"iPhone4,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1110, "length": 11, "value": "\"iPhone4,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1123, "length": 11, "value": "\"iPhone4,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1151, "length": 11, "value": "\"iPhone 4s\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1174, "length": 11, "value": "\"iPhone5,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1187, "length": 11, "value": "\"iPhone5,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1215, "length": 10, "value": "\"iPhone 5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1237, "length": 11, "value": "\"iPhone5,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1250, "length": 11, "value": "\"iPhone5,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1278, "length": 11, "value": "\"iPhone 5c\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1301, "length": 11, "value": "\"iPhone6,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1314, "length": 11, "value": "\"iPhone6,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1342, "length": 11, "value": "\"iPhone 5s\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1365, "length": 11, "value": "\"iPhone7,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1393, "length": 10, "value": "\"iPhone 6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1415, "length": 11, "value": "\"iPhone7,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1443, "length": 15, "value": "\"iPhone 6 Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1470, "length": 11, "value": "\"iPhone8,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1498, "length": 11, "value": "\"iPhone 6s\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1521, "length": 11, "value": "\"iPhone8,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1549, "length": 16, "value": "\"iPhone 6s Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1577, "length": 11, "value": "\"iPhone9,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1590, "length": 11, "value": "\"iPhone9,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1618, "length": 10, "value": "\"iPhone 7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1640, "length": 11, "value": "\"iPhone9,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1653, "length": 11, "value": "\"iPhone9,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1681, "length": 15, "value": "\"iPhone 7 Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1708, "length": 12, "value": "\"iPhone10,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1722, "length": 12, "value": "\"iPhone10,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1751, "length": 10, "value": "\"iPhone 8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1773, "length": 12, "value": "\"iPhone10,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1787, "length": 12, "value": "\"iPhone10,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1816, "length": 15, "value": "\"iPhone 8 Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1843, "length": 12, "value": "\"iPhone10,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1857, "length": 12, "value": "\"iPhone10,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1886, "length": 10, "value": "\"iPhone X\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1908, "length": 12, "value": "\"iPhone11,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1937, "length": 11, "value": "\"iPhone XS\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1960, "length": 12, "value": "\"iPhone11,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 1974, "length": 12, "value": "\"iPhone11,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2003, "length": 15, "value": "\"iPhone XS Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2030, "length": 12, "value": "\"iPhone11,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2059, "length": 11, "value": "\"iPhone XR\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2082, "length": 12, "value": "\"iPhone12,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2111, "length": 11, "value": "\"iPhone 11\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2134, "length": 12, "value": "\"iPhone12,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2163, "length": 15, "value": "\"iPhone 11 Pro\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2190, "length": 12, "value": "\"iPhone12,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2219, "length": 19, "value": "\"iPhone 11 Pro Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2250, "length": 12, "value": "\"iPhone13,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2279, "length": 16, "value": "\"iPhone 12 mini\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2307, "length": 12, "value": "\"iPhone13,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2336, "length": 11, "value": "\"iPhone 12\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2359, "length": 12, "value": "\"iPhone13,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2388, "length": 15, "value": "\"iPhone 12 Pro\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2415, "length": 12, "value": "\"iPhone13,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2444, "length": 19, "value": "\"iPhone 12 Pro Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2475, "length": 12, "value": "\"iPhone14,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2504, "length": 16, "value": "\"iPhone 13 mini\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2532, "length": 12, "value": "\"iPhone14,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2561, "length": 11, "value": "\"iPhone 13\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2584, "length": 12, "value": "\"iPhone14,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2613, "length": 15, "value": "\"iPhone 13 Pro\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2640, "length": 12, "value": "\"iPhone14,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2669, "length": 19, "value": "\"iPhone 13 Pro Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2700, "length": 12, "value": "\"iPhone14,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2729, "length": 11, "value": "\"iPhone 14\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2752, "length": 12, "value": "\"iPhone14,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2781, "length": 16, "value": "\"iPhone 14 Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2809, "length": 12, "value": "\"iPhone15,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2838, "length": 15, "value": "\"iPhone 14 Pro\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2865, "length": 12, "value": "\"iPhone15,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2894, "length": 19, "value": "\"iPhone 14 Pro Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2925, "length": 11, "value": "\"iPhone8,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2953, "length": 11, "value": "\"iPhone SE\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 2976, "length": 12, "value": "\"iPhone12,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3005, "length": 28, "value": "\"iPhone SE (2nd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3045, "length": 12, "value": "\"iPhone14,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3074, "length": 28, "value": "\"iPhone SE (3rd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3114, "length": 12, "value": "\"iPhone15,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3143, "length": 11, "value": "\"iPhone 15\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3166, "length": 12, "value": "\"iPhone15,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3195, "length": 16, "value": "\"iPhone 15 Plus\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3223, "length": 12, "value": "\"iPhone16,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3252, "length": 15, "value": "\"iPhone 15 Pro\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3279, "length": 12, "value": "\"iPhone16,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3308, "length": 19, "value": "\"iPhone 15 Pro Max\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3339, "length": 9, "value": "\"iPad2,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3350, "length": 9, "value": "\"iPad2,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3361, "length": 9, "value": "\"iPad2,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3372, "length": 9, "value": "\"iPad2,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3398, "length": 8, "value": "\"iPad 2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3418, "length": 9, "value": "\"iPad3,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3429, "length": 9, "value": "\"iPad3,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3440, "length": 9, "value": "\"iPad3,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3466, "length": 23, "value": "\"iPad (3rd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3501, "length": 9, "value": "\"iPad3,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3512, "length": 9, "value": "\"iPad3,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3523, "length": 9, "value": "\"iPad3,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3549, "length": 23, "value": "\"iPad (4th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3584, "length": 10, "value": "\"iPad6,11\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3596, "length": 10, "value": "\"iPad6,12\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3623, "length": 23, "value": "\"iPad (5th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3658, "length": 9, "value": "\"iPad7,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3669, "length": 9, "value": "\"iPad7,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3695, "length": 23, "value": "\"iPad (6th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3730, "length": 10, "value": "\"iPad7,11\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3742, "length": 10, "value": "\"iPad7,12\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3769, "length": 23, "value": "\"iPad (7th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3804, "length": 10, "value": "\"iPad11,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3816, "length": 10, "value": "\"iPad11,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3843, "length": 23, "value": "\"iPad (8th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3878, "length": 10, "value": "\"iPad12,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3890, "length": 10, "value": "\"iPad12,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3917, "length": 23, "value": "\"iPad (9th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3952, "length": 11, "value": "\"iPad13,18\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3965, "length": 11, "value": "\"iPad13,19\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 3993, "length": 24, "value": "\"iPad (10th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4029, "length": 9, "value": "\"iPad4,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4040, "length": 9, "value": "\"iPad4,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4051, "length": 9, "value": "\"iPad4,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4077, "length": 10, "value": "\"iPad Air\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4099, "length": 9, "value": "\"iPad5,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4110, "length": 9, "value": "\"iPad5,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4136, "length": 12, "value": "\"iPad Air 2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4160, "length": 10, "value": "\"iPad11,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4172, "length": 10, "value": "\"iPad11,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4199, "length": 27, "value": "\"iPad Air (3rd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4238, "length": 10, "value": "\"iPad13,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4250, "length": 10, "value": "\"iPad13,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4277, "length": 27, "value": "\"iPad Air (4th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4316, "length": 11, "value": "\"iPad13,16\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4329, "length": 11, "value": "\"iPad13,17\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4357, "length": 27, "value": "\"iPad Air (5th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4396, "length": 9, "value": "\"iPad2,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4407, "length": 9, "value": "\"iPad2,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4418, "length": 9, "value": "\"iPad2,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4444, "length": 11, "value": "\"iPad mini\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4467, "length": 9, "value": "\"iPad4,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4478, "length": 9, "value": "\"iPad4,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4489, "length": 9, "value": "\"iPad4,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4515, "length": 13, "value": "\"iPad mini 2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4540, "length": 9, "value": "\"iPad4,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4551, "length": 9, "value": "\"iPad4,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4562, "length": 9, "value": "\"iPad4,9\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4588, "length": 13, "value": "\"iPad mini 3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4613, "length": 9, "value": "\"iPad5,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4624, "length": 9, "value": "\"iPad5,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4650, "length": 13, "value": "\"iPad mini 4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4675, "length": 10, "value": "\"iPad11,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4687, "length": 10, "value": "\"iPad11,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4714, "length": 28, "value": "\"iPad mini (5th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4754, "length": 10, "value": "\"iPad14,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4766, "length": 10, "value": "\"iPad14,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4793, "length": 28, "value": "\"iPad mini (6th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4833, "length": 9, "value": "\"iPad6,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4844, "length": 9, "value": "\"iPad6,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4870, "length": 21, "value": "\"iPad Pro (9.7-inch)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4903, "length": 9, "value": "\"iPad7,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4914, "length": 9, "value": "\"iPad7,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4940, "length": 22, "value": "\"iPad Pro (10.5-inch)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4974, "length": 9, "value": "\"iPad8,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4985, "length": 9, "value": "\"iPad8,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 4996, "length": 9, "value": "\"iPad8,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5007, "length": 9, "value": "\"iPad8,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5033, "length": 37, "value": "\"iPad Pro (11-inch) (1st generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5082, "length": 9, "value": "\"iPad8,9\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5093, "length": 10, "value": "\"iPad8,10\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5120, "length": 37, "value": "\"iPad Pro (11-inch) (2nd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5169, "length": 10, "value": "\"iPad13,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5181, "length": 10, "value": "\"iPad13,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5193, "length": 10, "value": "\"iPad13,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5205, "length": 10, "value": "\"iPad13,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5232, "length": 37, "value": "\"iPad Pro (11-inch) (3rd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5281, "length": 10, "value": "\"iPad14,3\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5293, "length": 10, "value": "\"iPad14,4\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5320, "length": 37, "value": "\"iPad Pro (11-inch) (4th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5369, "length": 9, "value": "\"iPad6,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5380, "length": 9, "value": "\"iPad6,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5406, "length": 39, "value": "\"iPad Pro (12.9-inch) (1st generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5457, "length": 9, "value": "\"iPad7,1\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5468, "length": 9, "value": "\"iPad7,2\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5494, "length": 39, "value": "\"iPad Pro (12.9-inch) (2nd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5545, "length": 9, "value": "\"iPad8,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5556, "length": 9, "value": "\"iPad8,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5567, "length": 9, "value": "\"iPad8,7\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5578, "length": 9, "value": "\"iPad8,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5604, "length": 39, "value": "\"iPad Pro (12.9-inch) (3rd generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5655, "length": 10, "value": "\"iPad8,11\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5667, "length": 10, "value": "\"iPad8,12\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5694, "length": 39, "value": "\"iPad Pro (12.9-inch) (4th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5745, "length": 10, "value": "\"iPad13,8\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5757, "length": 10, "value": "\"iPad13,9\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5769, "length": 11, "value": "\"iPad13,10\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5782, "length": 11, "value": "\"iPad13,11\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5810, "length": 39, "value": "\"iPad Pro (12.9-inch) (5th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5861, "length": 10, "value": "\"iPad14,5\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5873, "length": 10, "value": "\"iPad14,6\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5900, "length": 39, "value": "\"iPad Pro (12.9-inch) (6th generation)\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5951, "length": 6, "value": "\"i386\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5959, "length": 8, "value": "\"x86_64\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 5969, "length": 7, "value": "\"arm64\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 6047, "length": 28, "value": "\"SIMULATOR_MODEL_IDENTIFIER\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 6084, "length": 5, "value": "\"iOS\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 6106, "length": 50, "value": "\"Simulator \""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/UIDevice.swift", "kind": "StringLiteral", "offset": 6155, "length": 1, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "IntegerLiteral", "offset": 1281, "length": 3, "value": "100"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1354, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1420, "length": 6, "value": "\"Bank\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1494, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1560, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1614, "length": 25, "value": "\"Ekyc_ndid_holding_title\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1648, "length": 35, "value": "\"ยืนยันตัวตน\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1732, "length": 38, "value": "\"Ekyc_ndid_holding_authenticate_title\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1779, "length": 119, "value": "\"กรุณายืนยันตัวตนตามแอปธนาคารที่เลือกไว้\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 1952, "length": 39, "value": "\"Ekyc_ndid_holding_authenticate_detail\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2010, "length": 241, "value": "\"ท่านกำลังยืนยันตัวตนเพื่อใช้ตามวัตถุประสงค์ของ SCBS และ ประสงค์ให้ส่งข้อมูลจาก ธนาคาร\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2300, "length": 41, "value": "\"Ekyc_ndid_holding_verify_identification\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2360, "length": 176, "value": "\"กรุณายืนยันตัวตนในแอป ภายใน 60 นาที และกลับมาทำรายการต่อที่นี่\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2592, "length": 32, "value": "\"Ekyc_ndid_holding_referralCode\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2646, "length": 11, "value": "\"Ref code:\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2710, "length": 24, "value": "\"Eky<PERSON>_ndid_holding_open\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2753, "length": 23, "value": "\"เปิดแอป\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2831, "length": 33, "value": "\"Ekyc_ndid_holding_cancel_button\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2873, "length": 62, "value": "\"ยกเลิกการยืนยันตัวตน\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 2990, "length": 27, "value": "\"Ekyc_ndid_holding_confirm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 3026, "length": 74, "value": "\"ยืนยันตัวตนเรียบร้อยแล้ว\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 3158, "length": 28, "value": "\"Ekyc_ndid_navigation_title\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentPresenter.swift", "kind": "StringLiteral", "offset": 3195, "length": 44, "value": "\"เปิดบัญชีลงทุน\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/DateInputTextView/DateInputTextView.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2947, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/EkycFrameworkKit.swift", "kind": "StringLiteral", "offset": 449, "length": 4, "value": "\"en\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/EkycFrameworkKit.swift", "kind": "StringLiteral", "offset": 505, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/EkycFrameworkKit.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 895, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/EkycFrameworkKit.swift", "kind": "StringLiteral", "offset": 935, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/InputTextWithValidation/InputTextWithValidation.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 9764, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDScanProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 327, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDScanProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 4921, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDScanProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 7691, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDScanProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 9482, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/EnrollmentProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 311, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/LoadingView/LoadingViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 218, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 306, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 342, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 378, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 412, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 458, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 502, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 585, "length": 28, "value": "\"CFBundleShortVersionString\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 634, "length": 7, "value": "\"0.0.0\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 678, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 720, "length": 30, "value": "\"/v1/ekyc/authen/sessiontoken\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 800, "length": 38, "value": "\"/v1/ekyc/authen/sessiontoken/facetec\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 880, "length": 24, "value": "\"/v1/ekyc/enrollment-3d\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 951, "length": 29, "value": "\"/v1/ekyc/match-3d-2d-idscan\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1024, "length": 28, "value": "\"/v1/ekyc/confirmation-info\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1094, "length": 22, "value": "\"/v1/ekyc/ndid/status\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1165, "length": 30, "value": "\"/v1/ekyc/ndid/request/cancel\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1234, "length": 19, "value": "\"/v1/ekyc/ndid/idp\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1296, "length": 23, "value": "\"/v1/ekyc/ndid/request\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1359, "length": 20, "value": "\"/v1/ekyc/init-flow\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 1424, "length": 22, "value": "\"/v1/ekyc/idscan-only\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 4368, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4421, "length": 5, "value": "\"DEV\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4442, "length": 5, "value": "\"SIT\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4463, "length": 5, "value": "\"UAT\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4488, "length": 4, "value": "\"PT\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4512, "length": 9, "value": "\"PREPROD\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Config.swift", "kind": "StringLiteral", "offset": 4538, "length": 6, "value": "\"PROD\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2415, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2465, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2515, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2563, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2612, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2662, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2712, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2760, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2809, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2859, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2909, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2960, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/DocumentReviewViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3006, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/model/OcrIdCardVerifyByFaceModel.swift", "kind": "StringLiteral", "offset": 423, "length": 10, "value": "\"liveness\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/model/OcrIdCardVerifyByFaceModel.swift", "kind": "StringLiteral", "offset": 447, "length": 5, "value": "\"ocr\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 221, "length": 12, "value": "0.2549019608"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 242, "length": 12, "value": "0.4980392157"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 262, "length": 12, "value": "0.6980392157"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 283, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 333, "length": 12, "value": "0.1411764771"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 354, "length": 12, "value": "0.3960784376"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 374, "length": 12, "value": "0.5647059083"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 395, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 442, "length": 8, "value": "1.000000"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 459, "length": 8, "value": "1.000000"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 475, "length": 8, "value": "1.000000"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 492, "length": 8, "value": "1.000000"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 546, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 556, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 565, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "IntegerLiteral", "offset": 575, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Colors.swift", "kind": "FloatLiteral", "offset": 748, "length": 3, "value": "1.0"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Networking/Rest.swift", "kind": "StringLiteral", "offset": 158, "length": 5, "value": "\"PUT\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Networking/Rest.swift", "kind": "StringLiteral", "offset": 177, "length": 5, "value": "\"GET\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Networking/Rest.swift", "kind": "StringLiteral", "offset": 197, "length": 6, "value": "\"POST\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Networking/Rest.swift", "kind": "StringLiteral", "offset": 220, "length": 8, "value": "\"DELETE\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 478, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 519, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 564, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 609, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 653, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 697, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 742, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 787, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 833, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/OcrPrefillViewModel.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 874, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 377, "length": 13, "value": "\"dd MMM yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 417, "length": 14, "value": "\"dd MMMM yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 460, "length": 10, "value": "\"MMM yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 496, "length": 23, "value": "\"dd MMM yyyy - HH:mm a\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 551, "length": 21, "value": "\"dd MMM yyyy - HH:mm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 615, "length": 19, "value": "\"dd MMM yyyy HH:mm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 665, "length": 16, "value": "\"dd MMM - HH:mm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 704, "length": 8, "value": "\"dd MMM\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 734, "length": 7, "value": "\"HH:mm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 757, "length": 12, "value": "\"yyyy-MM-dd\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 786, "length": 27, "value": "\"yyyy-M-dd'T'HH:mm:ss.SSSZ\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 828, "length": 28, "value": "\"yyyy-MM-dd'T'HH:mm:ssZZZZZ\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 871, "length": 30, "value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 916, "length": 32, "value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 963, "length": 10, "value": "\"HH:mm:ss\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1007, "length": 10, "value": "\"ddMMyyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1044, "length": 12, "value": "\"dd/MM/yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1092, "length": 12, "value": "\"yyyy/dd/MM\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1134, "length": 23, "value": "\"yyyy-MM-dd'T'HH:mm:ss\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1183, "length": 6, "value": "\"yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1236, "length": 10, "value": "\"-/-/yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1277, "length": 11, "value": "\"-/MM/yyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1333, "length": 9, "value": "\"MMMyyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Date.swift", "kind": "StringLiteral", "offset": 1382, "length": 11, "value": "\"ddMMMyyyy\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 546, "length": 12, "value": "\"Error text\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 579, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 606, "length": 4, "value": "\"OK\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 1195, "length": 12, "value": "\"Error text\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 1228, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 1255, "length": 8, "value": "\"Cancel\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 1294, "length": 9, "value": "\"Confirm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2223, "length": 12, "value": "\"Error text\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2285, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2341, "length": 4, "value": "\"OK\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2735, "length": 12, "value": "\"Error text\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2797, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2853, "length": 8, "value": "\"Cancel\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Alert/AlertViewController.swift", "kind": "StringLiteral", "offset": 2921, "length": 9, "value": "\"Confirm\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/PickerDropdownButton/PickerDropdownButton.swift", "kind": "Array", "offset": 1687, "length": 19, "value": "[\"Mr\", \"Mrs\", \"Ms\"]"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/PickerDropdownButton/PickerDropdownButton.swift", "kind": "StringLiteral", "offset": 1735, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Components/Picker/PickerViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 414, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3192, "length": 11, "value": "\"EKYCTOKEN\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3242, "length": 16, "value": "\"SESSIONFACETEC\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3302, "length": 21, "value": "\"DEVICEKEYIDENTIFIER\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3375, "length": 29, "value": "\"PUBLICFACESCANENCRYPTIONKEY\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3442, "length": 15, "value": "\"PRODUCTIONKEY\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3498, "length": 18, "value": "\"SDKENCRYPTIONKEY\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3556, "length": 17, "value": "\"SDKENCRYPTIONIV\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/AppProperty.swift", "kind": "StringLiteral", "offset": 3612, "length": 16, "value": "\"INSTALLATIONID\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 216, "length": 19, "value": "\"#-####-#####-##-#\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 273, "length": 19, "value": "\"# #### ##### ## #\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 332, "length": 19, "value": "\"#-####-####x-xx-x\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 391, "length": 19, "value": "\"# #### ####x xx x\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 487, "length": 12, "value": "\"##/##/####\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 565, "length": 16, "value": "\"###-#######-##\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 649, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 675, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/StringFormat.swift", "kind": "StringLiteral", "offset": 2087, "length": 3, "value": "\"#\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 586, "length": 18, "value": "\"CheckBox_Checked\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 674, "length": 20, "value": "\"CheckBox_Unchecked\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 751, "length": 10, "value": "\"BackIcon\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 843, "length": 23, "value": "\"FaceTec_your_app_logo\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 938, "length": 22, "value": "\"FaceTec_active_torch\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 1034, "length": 24, "value": "\"FaceTec_inactive_torch\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 1125, "length": 18, "value": "\"FaceTec_document\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 1210, "length": 16, "value": "\"FaceTec_camera\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Constants/Images.swift", "kind": "StringLiteral", "offset": 1292, "length": 16, "value": "\"FaceTec_cancel\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/Extension/DocumentReviewValidation.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 816, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/Extension/DocumentReviewValidation.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1796, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/Extension/DocumentReviewValidation.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2456, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/DocumentReview/Extension/DocumentReviewValidation.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3125, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/model/InitEkycModel.swift", "kind": "StringLiteral", "offset": 1455, "length": 5, "value": "\"GET\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentViewController.swift", "kind": "StringLiteral", "offset": 1330, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentViewController.swift", "kind": "StringLiteral", "offset": 1521, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentViewController.swift", "kind": "StringLiteral", "offset": 1561, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Feature/NdidVerification/NdidVerificationEnrollment/NdidVerificationEnrollmentViewController.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1599, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDMatchProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 370, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDMatchProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 406, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDMatchProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6650, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDMatchProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 8794, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Source/Processor/PhotoIDMatchProcessor.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 11132, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 203, "length": 18, "value": "\"^[a-zA-Z. …]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 263, "length": 16, "value": "\"^[a-zA-Z. -]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 321, "length": 37, "value": "\"^[\\p{<PERSON>ript=Thai}\\p{Mn}\\s.…]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 401, "length": 35, "value": "\"^[\\p{<PERSON>rip<PERSON>=Thai}\\p{Mn}\\s.-]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 481, "length": 30, "value": "\"^[\\p{<PERSON>ript=Thai}\\p{Mn}]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 558, "length": 16, "value": "\"^[0-9a-zA-Z]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 602, "length": 10, "value": "\"^[0-9]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 645, "length": 11, "value": "\"^[0-9-]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 684, "length": 11, "value": "\"^[0-9/]+$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 726, "length": 14, "value": "\"^[A-Z0-9-]*$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 777, "length": 10, "value": "\"^[A-Z]*$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 824, "length": 11, "value": "\"^[0-9-]*$\""}, {"filePath": "/Users/<USER>/Documents/iOS/ekyc-framework-ios/EkycFramework/EkycFramework/Helper/Regex.swift", "kind": "StringLiteral", "offset": 861, "length": 8, "value": "\"^[-]+$\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 729, "length": 10, "value": "\"BackIcon\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 872, "length": 14, "value": "\"Bank_default\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1027, "length": 18, "value": "\"CheckBox_Checked\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1190, "length": 20, "value": "\"CheckBox_Unchecked\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1358, "length": 22, "value": "\"FaceTec_active_torch\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1517, "length": 16, "value": "\"FaceTec_camera\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1670, "length": 16, "value": "\"FaceTec_cancel\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1827, "length": 18, "value": "\"FaceTec_document\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1997, "length": 24, "value": "\"FaceTec_inactive_torch\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2170, "length": 23, "value": "\"FaceTec_your_app_logo\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2325, "length": 13, "value": "\"SuccessIcon\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2480, "length": 19, "value": "\"icon_chevron_down\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2641, "length": 19, "value": "\"icon_chevron_left\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2804, "length": 20, "value": "\"icon_chevron_right\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 2953, "length": 12, "value": "\"kbank_logo\""}, {"filePath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/EkycFramework-hczhenietnmqrjaftnjwltzgtinq/Build/Intermediates.noindex/ArchiveIntermediates/EkycFramework/IntermediateBuildFilesPath/EkycFramework.build/Release-iphonesimulator/EkycFramework.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 3090, "length": 10, "value": "\"scb_logo\""}]}