<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		dVxFFGAV/7s76tFB+X5lXgXHh+w=
		</data>
		<key>DateInputTextView.nib</key>
		<data>
		xH5s8kTpYMCgrCfhb5MS0as2NmM=
		</data>
		<key>DocumentReview.storyboardc/DocumentReview.nib/objects-11.0+.nib</key>
		<data>
		bepVlaQLNGTgsFlrRrtwqU399MQ=
		</data>
		<key>DocumentReview.storyboardc/DocumentReview.nib/runtime.nib</key>
		<data>
		bepVlaQLNGTgsFlrRrtwqU399MQ=
		</data>
		<key>DocumentReview.storyboardc/Info.plist</key>
		<data>
		MDSQqqxgs8f9ih3MPLF/wlbbjMA=
		</data>
		<key>DocumentReview.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib/objects-11.0+.nib</key>
		<data>
		4qc3tXS74XdXqNB3svVmrWeoUbU=
		</data>
		<key>DocumentReview.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib/runtime.nib</key>
		<data>
		ndTAca97qfh35SiY3OF8kQkvY+g=
		</data>
		<key>EnableCamera.storyboardc/EnableCamera.nib</key>
		<data>
		kOSJpVCXX+lbwbNiAiQLyiBm3Po=
		</data>
		<key>EnableCamera.storyboardc/Info.plist</key>
		<data>
		Vt1DQfjcO2OKdsQ4s/HJoD7hrJA=
		</data>
		<key>EnableCamera.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		sOn1+O3YdXzOel++F5hiDf7KiOU=
		</data>
		<key>FaceTec_OCR_Customization.json</key>
		<data>
		0VZiF7llaW269ViSFGcXXiLI3lg=
		</data>
		<key>Headers/EkycFramework-Swift.h</key>
		<data>
		f0A/bsxBnMhFuiyhSeKgZZQH9e0=
		</data>
		<key>Headers/EkycFramework.h</key>
		<data>
		omSzcVVJuEX9hnu35iX0uRruJRc=
		</data>
		<key>Info.plist</key>
		<data>
		4O3Eu2wYyat4eGnwX8RpoLMMT2U=
		</data>
		<key>InputTextWithValidation.nib</key>
		<data>
		nFg551ViaeKjE6vpxG40XMdGToc=
		</data>
		<key>LoadingViewController.storyboardc/Info.plist</key>
		<data>
		ypz8vQT+u4RPl9Fq3L0v8SydZkc=
		</data>
		<key>LoadingViewController.storyboardc/LoadingViewController.nib</key>
		<data>
		HeZm/eKTzyJzacbBg/LReH7fxcA=
		</data>
		<key>LoadingViewController.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		0LGkGUgFAQB6SW/RAsy02De/tps=
		</data>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		6Ur8UWKW7MQzIFmuEDhgBKB8OoY=
		</data>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		4LIZ5LPmzdJPRHBz8ViOTzwW60s=
		</data>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		rQSxqSznoHUzwDOzASvvNyzqARs=
		</data>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		4LIZ5LPmzdJPRHBz8ViOTzwW60s=
		</data>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		Mxzi/0L/DYGlm+gQAUcJUKLHeGc=
		</data>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		6Ur8UWKW7MQzIFmuEDhgBKB8OoY=
		</data>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		COpD+6tres8oO2ATLmPn9lCuJHU=
		</data>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		Npoc/avUUdyv0CNs0ZNKMjF/NrQ=
		</data>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		COpD+6tres8oO2ATLmPn9lCuJHU=
		</data>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		tt00UDJ3Yo1XrBmwnmZtccrKXhM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		2flrJuggvVHJQ6bQkAlYa4l009E=
		</data>
		<key>NdidVerificationEnrollment.storyboardc/Info.plist</key>
		<data>
		Ha33NIxCxD3PBSyrEIRBF0MNiEY=
		</data>
		<key>NdidVerificationEnrollment.storyboardc/NdidVerificationEnrollment.nib</key>
		<data>
		IRoWi2osRAxjrjBXqVxwbHyVRuA=
		</data>
		<key>NdidVerificationEnrollment.storyboardc/UINavigationController-aUL-qR-SOa.nib</key>
		<data>
		74WmCTsOytmZ4dzKypkYFD4MCvE=
		</data>
		<key>NdidVerificationEnrollment.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		PlEjbPY3ibvOXzd5utlyLIVGmJU=
		</data>
		<key>NdidVerificationSelectIdp.storyboardc/Info.plist</key>
		<data>
		a2s7Soca/He3q2p/t0WaGfeHweU=
		</data>
		<key>NdidVerificationSelectIdp.storyboardc/NdidVerificationSelectIdp.nib</key>
		<data>
		zSs82iyX3Cvs52xzjWiEIZtwvWk=
		</data>
		<key>NdidVerificationSelectIdp.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		KKk1slKfW/n/Cmya2oXItMCf6JQ=
		</data>
		<key>NdidVerificationSuccessful.storyboardc/Info.plist</key>
		<data>
		XcCsaAT4y+vn038IAwAMW2z36FU=
		</data>
		<key>NdidVerificationSuccessful.storyboardc/NdidVerificationSuccessful.nib</key>
		<data>
		OegrCzK1/1Js9xX9EWcel5Z+ybM=
		</data>
		<key>NdidVerificationSuccessful.storyboardc/UINavigationController-NKe-Dj-Ojj.nib</key>
		<data>
		qV3EwvjovB11+BjN7kj/QAW3G+Y=
		</data>
		<key>NdidVerificationSuccessful.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		ltv0JL4TaDQdDXAy89vWj4FnVeI=
		</data>
		<key>OcrIdCardPreLoad.storyboardc/Info.plist</key>
		<data>
		fNDe4iXt4vqgy4XZn+CBlNQMDK8=
		</data>
		<key>OcrIdCardPreLoad.storyboardc/OcrIdCardPreLoad.nib</key>
		<data>
		pb+NQge6GBbn4Ld/zvwVv8V6f/s=
		</data>
		<key>OcrIdCardPreLoad.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		iO6FhGbm7YVpaWc2NBA/7vQahXU=
		</data>
		<key>OcrIdCardScan.storyboardc/Info.plist</key>
		<data>
		U+R03tT146w/hVVTiOBqL2S2H7k=
		</data>
		<key>OcrIdCardScan.storyboardc/OcrIdCardScan.nib</key>
		<data>
		1MvjdKXgfuvKzaE1OFmj6NnAh9w=
		</data>
		<key>OcrIdCardScan.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<data>
		yD3Z6MzLlMOzfMDG4Md2GLm3M80=
		</data>
		<key>PickerDateViewController.nib/objects-14.0+.nib</key>
		<data>
		o5I1S1Pwmg+sRfpBC+7/UN9yh1U=
		</data>
		<key>PickerDateViewController.nib/runtime.nib</key>
		<data>
		dBXVdkNekCKSJVJ2WOaP/dWGISg=
		</data>
		<key>PickerDropdownButton.nib</key>
		<data>
		hhdZFZ+tkZdkwEE34RPTV5BWRTA=
		</data>
		<key>PickerViewController.nib</key>
		<data>
		f5aZoIAJHEs1ubpMR7gQaGfWg9M=
		</data>
		<key>SelectBankView.nib</key>
		<data>
		Md8f948tpGVCems+h1F3q0v0B18=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			kzsOzdMumF01Mvs2ydmKN8iRixu1exBdbbg38wCDnH8=
			</data>
		</dict>
		<key>DateInputTextView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			G/a+9w6zjBV5RkexU0+nMuX8r4FYDx+sxcClmjTVhxY=
			</data>
		</dict>
		<key>DocumentReview.storyboardc/DocumentReview.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			riWPtz9w4I8L0BoZOC1YfXK2MCKm/8QDn9rV9EuNISY=
			</data>
		</dict>
		<key>DocumentReview.storyboardc/DocumentReview.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			riWPtz9w4I8L0BoZOC1YfXK2MCKm/8QDn9rV9EuNISY=
			</data>
		</dict>
		<key>DocumentReview.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MXOgWbxUO9XIIIFAxWxGYZH4GLKBWpSOmVN+kag0XlY=
			</data>
		</dict>
		<key>DocumentReview.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			6AnmkjZs7hRSYwATNxbYtktiGK5I8Amo07RcBpbBh7g=
			</data>
		</dict>
		<key>DocumentReview.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			CM3aFhEOO1WiO0WpN3n7uYLP+1PpIjqjmsC6jxhwT60=
			</data>
		</dict>
		<key>EnableCamera.storyboardc/EnableCamera.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			E2xVr8yfxHeyJj8D8HGdlzV5t7oivl08sVaDLrdnv0k=
			</data>
		</dict>
		<key>EnableCamera.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			QwgGiI5vYW0ageFbyEuw04dEFJvS7/K2gIhQEg/ux2U=
			</data>
		</dict>
		<key>EnableCamera.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VEDhKixYoBEfQM2ZENqkEX4BoCPwVnFP7QnBM6wqSWU=
			</data>
		</dict>
		<key>FaceTec_OCR_Customization.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SdgSf8obXYsXEfjCKbuScV5+t/l4cff+E4mt9lX5btk=
			</data>
		</dict>
		<key>Headers/EkycFramework-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			s4YwFNZazlUtNsgoriOKS6In/OByjTk77ho0C8UgTUc=
			</data>
		</dict>
		<key>Headers/EkycFramework.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EGTxGJsIaPXEb6yU8kLFuWje9j0NXIM+h1mS6I+sqfw=
			</data>
		</dict>
		<key>InputTextWithValidation.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BR7UANJg2ZYaGdU7nwsFYWgsIYTiORTq4GQGvRR2gmQ=
			</data>
		</dict>
		<key>LoadingViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ThTT9p18F7/1aTCiYDqumB7JWufnOsOxgMBa98+p1j8=
			</data>
		</dict>
		<key>LoadingViewController.storyboardc/LoadingViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7xXcgGv4Bm8rZoc7gls4wiqSYsjb+W6EKQnjn9l/iOE=
			</data>
		</dict>
		<key>LoadingViewController.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XTH+Qj7D6a++L3JrDlYNT9ARyB7oa+hdAof3LMGDc9I=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YkBgUpbf8rjukJetI8aueXXM+j3qQg+x+3X1HIsCCHY=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WAXTb0BOK2aO+timcVFqxCzYkFGJxW4ZtEkSnH2p0Og=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			yXNF3UFQyf3CIkPvzrY11A4WxAgEd/FmVYPKiVDSS4w=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WAXTb0BOK2aO+timcVFqxCzYkFGJxW4ZtEkSnH2p0Og=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			johZKj5RLSUCjg3A6HoMfCepHgno9jdueBGQfL1amxU=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YkBgUpbf8rjukJetI8aueXXM+j3qQg+x+3X1HIsCCHY=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			6bYwJ5w96kS9TrtnFyOWPzimC8AqhByVW5vUZdnGhvY=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			upkf0Pkou7bczP2vFjLyeNz+bNNVzOsffCBPgk30DnY=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			6bYwJ5w96kS9TrtnFyOWPzimC8AqhByVW5vUZdnGhvY=
			</data>
		</dict>
		<key>Modules/EkycFramework.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			bsBwDtoGaVQU7jOcmmZn2WNXAsUvhgK9IlEvKlw20Fc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			z5HTrbxfIn2AkUM33ttt0CBGbd7JaoGzE0ItpoXY5HQ=
			</data>
		</dict>
		<key>NdidVerificationEnrollment.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			fZqdeLjYxV6ZTQpFnUkgKZdDcwlesdavf3q1Y66145k=
			</data>
		</dict>
		<key>NdidVerificationEnrollment.storyboardc/NdidVerificationEnrollment.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			QnbBrkfAEYTJE49YUIWb4j8QVLBQVmL7lByswUjtqnY=
			</data>
		</dict>
		<key>NdidVerificationEnrollment.storyboardc/UINavigationController-aUL-qR-SOa.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XkQF0ENx9AD6Q7xBUYT/hHaI5ic9FEdmDUBMgkOIkBQ=
			</data>
		</dict>
		<key>NdidVerificationEnrollment.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			xkRsNZj711iJeUGZ8oNBfJTD2069F1aeA2J6Qnh+0xQ=
			</data>
		</dict>
		<key>NdidVerificationSelectIdp.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			i/a0LXhT5Zzx+Ah+wZKH1bjtobmiohODMAmzqssUTz4=
			</data>
		</dict>
		<key>NdidVerificationSelectIdp.storyboardc/NdidVerificationSelectIdp.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			OjXq2eUtJjJyT0mbOYlguyqJ8EZS0RAOFuWvA9RFgKQ=
			</data>
		</dict>
		<key>NdidVerificationSelectIdp.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5ClzAZYn0CQ/iAL7qLwxMzrAqSnxQK1Gg69sxOPhGQ0=
			</data>
		</dict>
		<key>NdidVerificationSuccessful.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ft31AvUE+v0XBdDdum4TMGkk9lB+MyapZ3MApn2ZAx8=
			</data>
		</dict>
		<key>NdidVerificationSuccessful.storyboardc/NdidVerificationSuccessful.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			D3E9LQG4o0h7xJ3jUG/sUJigjylK6XuPxJJQXmNlyxo=
			</data>
		</dict>
		<key>NdidVerificationSuccessful.storyboardc/UINavigationController-NKe-Dj-Ojj.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pmCddafPLYDjViLJicyxM2uZ6O+a1HL+4tYdIUkuyFQ=
			</data>
		</dict>
		<key>NdidVerificationSuccessful.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			70QC1/q2sGxSJsUMPfBcSMWPHkiPkz14RWfyKBFWVWw=
			</data>
		</dict>
		<key>OcrIdCardPreLoad.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FcvYBqnN4yhullyVt8Tbr//0hB5OBDrTD6LpkqHv3w4=
			</data>
		</dict>
		<key>OcrIdCardPreLoad.storyboardc/OcrIdCardPreLoad.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			jNsoSlx97HraxLrnz2yj4kZezMESNuv8Nj1aU9x8GkM=
			</data>
		</dict>
		<key>OcrIdCardPreLoad.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XNfu/B7sxEWXIpQQkGTvOFPkH0nBkl7G1NsbJuh3EZc=
			</data>
		</dict>
		<key>OcrIdCardScan.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YrhFJC/zw1W4+upgaLnea/ncxXKKCbV0fE/GUwID0/A=
			</data>
		</dict>
		<key>OcrIdCardScan.storyboardc/OcrIdCardScan.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			flD1mkjapZBjeiQoSE+0AA6IiTYgMy3ozmVotCwinkg=
			</data>
		</dict>
		<key>OcrIdCardScan.storyboardc/Y6W-OH-hqX-view-5EZ-qb-Rvc.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			UV1Y+9irkSaMUVkcNnp0dh2+5WsoN0a2XNIJ6uAJv5o=
			</data>
		</dict>
		<key>PickerDateViewController.nib/objects-14.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			wcul2ZTc7mAQV7YHGd32tOwEF3SNMPIR6NErEvzMrD8=
			</data>
		</dict>
		<key>PickerDateViewController.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZCAaOcu+YEOjPFIlg4uqVWsHahzxg4ohSIf8gFg2C5s=
			</data>
		</dict>
		<key>PickerDropdownButton.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f6OJM8gPqVopY7ddibn+kKxbchL7OaJWLvFf3cYURDY=
			</data>
		</dict>
		<key>PickerViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XsPbS7SwcqqJU1BplE+99Nxs0gyPvRierog5oIfFohs=
			</data>
		</dict>
		<key>SelectBankView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ffa+gMXu66q5hZmf5eyz4fwNgnTfkl5rk1EZMPAIvHM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
