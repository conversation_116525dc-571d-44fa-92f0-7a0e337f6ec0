<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BRg-eH-hoe">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Main-->
        <scene sceneID="D9o-Yv-qxs">
            <objects>
                <viewController storyboardIdentifier="Main" title="Main" useStoryboardIdentifierAsRestorationIdentifier="YES" id="BRg-eH-hoe" customClass="MainViewController" customModule="EKYC_Demo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="FIO-Lp-Mdu">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zhD-Sx-13H">
                                <rect key="frame" x="186" y="437.5" width="42" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="v2n-ep-FwY">
                                <rect key="frame" x="132" y="508.5" width="150" height="35"/>
                                <color key="backgroundColor" systemColor="linkColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="150" id="6F8-eh-OcX"/>
                                    <constraint firstAttribute="height" constant="35" id="SNx-IH-OPV"/>
                                </constraints>
                                <state key="normal" title="Open Framework">
                                    <color key="titleColor" red="0.96078431369999995" green="0.95294117649999999" blue="0.96470588239999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="openFrameworkTapped:" destination="BRg-eH-hoe" eventType="touchUpInside" id="0lM-5q-avH"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Wg-MC-tE7">
                                <rect key="frame" x="157" y="563.5" width="100" height="35"/>
                                <color key="backgroundColor" systemColor="linkColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="100" id="Khk-az-91b"/>
                                    <constraint firstAttribute="height" constant="35" id="Kvp-41-rh3"/>
                                </constraints>
                                <state key="normal" title="Back to RN">
                                    <color key="titleColor" red="0.96078431369999995" green="0.95294117649999999" blue="0.96470588239999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="backButtonTapped:" destination="BRg-eH-hoe" eventType="touchUpInside" id="U0x-Vx-Z2g"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qsL-sR-CrH"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="v2n-ep-FwY" firstAttribute="top" secondItem="zhD-Sx-13H" secondAttribute="bottom" constant="50" id="16J-eo-4Z7"/>
                            <constraint firstItem="v2n-ep-FwY" firstAttribute="centerX" secondItem="FIO-Lp-Mdu" secondAttribute="centerX" id="6hu-3Z-Zab"/>
                            <constraint firstItem="5Wg-MC-tE7" firstAttribute="centerX" secondItem="FIO-Lp-Mdu" secondAttribute="centerX" id="72o-si-v9W"/>
                            <constraint firstItem="5Wg-MC-tE7" firstAttribute="top" secondItem="v2n-ep-FwY" secondAttribute="bottom" constant="20" id="JoB-Ko-54P"/>
                            <constraint firstItem="zhD-Sx-13H" firstAttribute="centerX" secondItem="FIO-Lp-Mdu" secondAttribute="centerX" id="MHa-a6-RMh"/>
                            <constraint firstItem="zhD-Sx-13H" firstAttribute="centerY" secondItem="FIO-Lp-Mdu" secondAttribute="centerY" id="d2N-au-O6G"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="D5R-CT-i0S"/>
                    <connections>
                        <outlet property="titleLabel" destination="zhD-Sx-13H" id="nda-74-AlP"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Map-Bw-d4f" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="151" y="-119"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="linkColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
