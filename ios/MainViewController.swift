//
//  MainViewController.swift
//  ReactNativeBridgeIos
//
//  Created by PATHOMPHONG CHAROENWICHIANCHAY on 15/10/21.
//

import Foundation
import EkycFramework

class MainViewController: UIViewController, URLSessionDelegate {
  
  @IBOutlet private weak var titleLabel: UILabel!
  
  override func viewDidLoad() {
    super.viewDidLoad()
    print("MyViewController loaded...")
    // standard view controller will load from RN
    setupSomething()
    print(BuildConfiguration.shared.environment)
  }
  
  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    navigationItem.title = "iOS Swift"
    navigationController?.setNavigationBarHidden(false, animated: animated)
  }
  
  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    navigationController?.setNavigationBarHidden(true, animated: animated)
  }
  
  @IBAction func backButtonTapped(_ sender: Any) {
    self.navigationController?.popViewController(animated: true)
  }
  
  @IBAction func openFrameworkTapped(_ sender: Any) {
    EkycFrameworkKit.initEkyc(sessionId: "sessionId",
                              token: "token",
                              environment: BuildConfiguration.shared.environment.rawValue,
                              customizeTheme: nil,
                              language: nil) { success, description, ekycToken  in
      if success {
        // TODO: Your code
      } else {
        // TODO: Your code
      }
    }
  }
  
  private func setupSomething() {
    titleLabel.text = "This screen is Native iOS by Viewcontroller"
  }
}

extension MainViewController {
  enum Environment: String {
    case dev = "DEV"
    case sit = "SIT"
    case uat = "UAT"
    case prod = "PROD"
  }
  
  class BuildConfiguration {
    static let shared = BuildConfiguration()
    
    var environment: Environment
    
    init() {
      let currentConfiguration = Bundle.main.object(forInfoDictionaryKey: "ENVIRONMENT") as! String
      environment = Environment(rawValue: currentConfiguration)!
    }
  }
}
