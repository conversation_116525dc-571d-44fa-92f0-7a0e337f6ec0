//
//  ConnectingFile.swift
//  ReactNativeBridgeIos
//
//  Created by PATHOMPHONG CHAROENWICHIANCHAY on 15/10/21.
//
import EkycFramework

@objc(Connect)
class Connect: NSObject {
  @objc static func requiresMainQueueSetup() -> Bool { return true }
  
  private func convertStringToBoolean(_ string: String?) -> Bool? {
    if (string == nil) {
      return nil
    }
    else {
      switch string?.lowercased() {
          case "true":
              return true
          case "false":
              return false
          default:
              return nil
          }
    }
  }
  
  @objc func goToOcrIdCardVerifyByFace(_ token: String, isCheckDopa checkDopa: Bool, isExpired checkExpiredIdCard: Bool, isEnableConfirm enableConfirmInfo: Bool, isPrefill: NSDictionary) -> Void {
    DispatchQueue.main.async {
      if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
        let titleThFlag = isPrefill["titleThFlag"] as? String
        let titleEnFlag = isPrefill["titleEnFlag"] as? String
        let firstNameThFlag = isPrefill["firstNameThFlag"] as? String
        let firstNameEnFlag = isPrefill["firstNameEnFlag"] as? String
        let lastNameThFlag = isPrefill["lastNameThFlag"] as? String
        let lastNameEnFlag = isPrefill["lastNameEnFlag"] as? String
        let dateOfBirthFlag = isPrefill["dateOfBirthFlag"] as? String
        let dateOfIssueFlag = isPrefill["dateOfIssueFlag"] as? String
        let dateOfExpiryFlag = isPrefill["dateOfExpiryFlag"] as? String
        let laserIdFlag = isPrefill["laserIdFlag"] as? String
        let ocrPrefill = OcrPrefill(titleThFlag: self.convertStringToBoolean(titleThFlag),
                                    titleEnFlag: self.convertStringToBoolean(titleEnFlag),
                                    firstNameThFlag: self.convertStringToBoolean(firstNameThFlag),
                                    firstNameEnFlag: self.convertStringToBoolean(firstNameEnFlag),
                                    lastNameThFlag: self.convertStringToBoolean(lastNameThFlag),
                                    lastNameEnFlag: self.convertStringToBoolean(lastNameEnFlag),
                                    dateOfBirthFlag: self.convertStringToBoolean(dateOfBirthFlag),
                                    dateOfIssueFlag: self.convertStringToBoolean(dateOfIssueFlag),
                                    dateOfExpiryFlag: self.convertStringToBoolean(dateOfExpiryFlag),
                                    laserIdFlag: self.convertStringToBoolean(laserIdFlag))
        appDelegate.goOcrIdCardVerifyByFaceStoryboard(token: token, checkDopa: checkDopa, checkExpiredIdCard: checkExpiredIdCard, enableConfirmInfo: enableConfirmInfo, ocrPrefill: ocrPrefill)
      }
    }
  }
  @objc func goToOcrIdCard(_ token: String, isCheckDopa checkDopa: Bool, isExpired checkExpiredIdCard: Bool, isEnableConfirm enableConfirmInfo: Bool, isPrefill: NSDictionary) -> Void {
    DispatchQueue.main.async {
      if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
        let titleThFlag = isPrefill["titleThFlag"] as? String
        let titleEnFlag = isPrefill["titleEnFlag"] as? String
        let firstNameThFlag = isPrefill["firstNameThFlag"] as? String
        let firstNameEnFlag = isPrefill["firstNameEnFlag"] as? String
        let lastNameThFlag = isPrefill["lastNameThFlag"] as? String
        let lastNameEnFlag = isPrefill["lastNameEnFlag"] as? String
        let dateOfBirthFlag = isPrefill["dateOfBirthFlag"] as? String
        let dateOfIssueFlag = isPrefill["dateOfIssueFlag"] as? String
        let dateOfExpiryFlag = isPrefill["dateOfExpiryFlag"] as? String
        let laserIdFlag = isPrefill["laserIdFlag"] as? String
        let ocrPrefill = OcrPrefill(titleThFlag: self.convertStringToBoolean(titleThFlag),
                                    titleEnFlag: self.convertStringToBoolean(titleEnFlag),
                                    firstNameThFlag: self.convertStringToBoolean(firstNameThFlag),
                                    firstNameEnFlag: self.convertStringToBoolean(firstNameEnFlag),
                                    lastNameThFlag: self.convertStringToBoolean(lastNameThFlag),
                                    lastNameEnFlag: self.convertStringToBoolean(lastNameEnFlag),
                                    dateOfBirthFlag: self.convertStringToBoolean(dateOfBirthFlag),
                                    dateOfIssueFlag: self.convertStringToBoolean(dateOfIssueFlag),
                                    dateOfExpiryFlag: self.convertStringToBoolean(dateOfExpiryFlag),
                                    laserIdFlag: self.convertStringToBoolean(laserIdFlag))
        appDelegate.goOcrIdCardStoryboard(token: token, checkDopa: checkDopa, checkExpiredIdCard: checkExpiredIdCard, enableConfirmInfo: enableConfirmInfo, ocrPrefill: ocrPrefill)
      }
    }
  }
  @objc func goToLiveness(_ token: String) -> Void {
    DispatchQueue.main.async {
      if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
        appDelegate.goLivenessStoryboard(token: token)
      }
    }
  }
  @objc func goToNdid(_ token: String, cidInput cid: String?) -> Void {
    DispatchQueue.main.async {
      if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
        appDelegate.goNdidStoryboard(token: token, cidNumber: cid)
      }
    }
  }
  @objc func callbackData(_ callback: RCTResponseSenderBlock) -> Void {
    let data = AppProperties().getUserConfirmValue()
    callback([data])
  }
}
