/*
 EkycLocalize.strings
 ReactNativeBridgeIos
 
 Created by PATHOMPHONG CHAROENWICHIANCHAY on 19/12/21.
 
 */

"FaceTec_accessibility_cancel_button" = "ยกเลิก";
"FaceTec_accessibility_torch_button" = "เปิดปิดแสงไฟ";

"FaceTec_action_ok" = "ตกลง";
"FaceTec_action_im_ready" = "ฉันพร้อมแล้ว";
"FaceTec_action_try_again" = "กรุณาลองอีกครั้ง";
"FaceTec_action_continue" = "ดำเนินการต่อ";
"FaceTec_action_take_photo" = "ถ่ายรูป";
"FaceTec_action_retake_photo" = "ถ่ายรูปอีกครั้ง";
"FaceTec_action_accept_photo" = "ยอมรับ";
"FaceTec_action_confirm" = "ยืนยันข้อมูล";
"FaceTec_action_scan_nfc" = "สแกนพาสปอร์ตอิเล็กทรอนิกส์";
"FaceTec_action_skip_nfc" = "ข้ามขั้นตอนนี้";

"FaceTec_camera_permission_header" = "เปิดใช้งานกล้อง";
"FaceTec_camera_permission_message_enroll" = "กรุณากดปุ่มด้านล่างเพื่อเปิดใช้งานกล้องหน้า";
"FaceTec_camera_permission_message_auth" = "คุณยังไม่ได้เปิดใช้งานกล้องถ่ายรูป แตะด้านล่างเพื่อเปลี่ยนแปลงการตั้งค่า";
"FaceTec_camera_permission_enable_camera" = "เปิดใช้งานกล้อง";
"FaceTec_camera_permission_launch_settings" = "เริ่มใช้งานการตั้งค่า";

"FaceTec_feedback_center_face" = "กรุณาขยับใบหน้าให้อยู่ตรงกลางกล้อง";
"FaceTec_feedback_face_not_found" = "กรุณาขยับใบหน้าให้อยู่ในกรอบ";
"FaceTec_feedback_face_not_looking_straight_ahead" = "มองตรงไปข้างหน้า";
"FaceTec_feedback_face_not_upright" = "ทำศีรษะให้ตรง";
"FaceTec_feedback_hold_steady" = "กรุณาอย่าขยับกล้อง";
"FaceTec_feedback_move_phone_away" = "คุณอยู่ใกล้กล้องเกินไป กรุณาขยับใบหน้าออก";
"FaceTec_feedback_move_phone_closer" = "คุณอยู่ห่างกล้องเกินไป กรุณาขยับใบหน้าใกล้ขึ้น";
"FaceTec_feedback_move_phone_to_eye_level" = "เลื่อนกล้องให้อยู่ในระดับสายตา";
"FaceTec_feedback_use_even_lighting" = "กรุณาจัดแสงไฟบนใบหน้าให้สว่างเท่ากัน";

"FaceTec_idscan_type_selection_header" = "เตรียมสแกนบัตรประชาชน";
"FaceTec_idscan_capture_tap_to_focus_message" = "แตะหน้าจอเพื่อโฟกัส";
"FaceTec_idscan_capture_hold_steady_message" = "กรุณาอย่าขยับกล้อง";
"FaceTec_idscan_capture_id_front_instruction_message" = "แสดงด้านหน้าบัตรประชาชน";
"FaceTec_idscan_capture_id_back_instruction_message" = "แสดงด้านหลังบัตรประชาชน";
"FaceTec_idscan_feedback_flip_id_to_back_message" = "แสดงด้านหลังบัตรประชาชน";
"FaceTec_idscan_review_id_front_instruction_message" = "ยืนยันว่าภาพถ่ายสามารถเห็นได้ชัดเจน";
"FaceTec_idscan_review_id_back_instruction_message" = "ยืนยันว่าภาพถ่ายสามารถเห็นได้ชัดเจน";
"FaceTec_idscan_ocr_confirmation_main_header" = "ตรวจสอบและยืนยัน";
"FaceTec_idscan_nfc_status_disabled_message" = "กรุณาเปิดใช้งาน NFC ที่การตั้งค่าอุปกรณ์ของคุณ เพื่อดำเนินการต่อ";
"FaceTec_idscan_nfc_status_ready_message" = "ระบบพร้อมที่จะสแกนชิพพาสปอร์ตอิเล็กทรอนิกส์ของคุณ";
"FaceTec_idscan_nfc_status_starting_message" = "ถือโทรศัพท์มือถือไว้ติดหลังพาสปอร์ตอิเล็กทรอนิกส์ เพื่อสแกนชิพ NFC";
"FaceTec_idscan_nfc_status_scanning_message" = "กำลังสแกนชิพพาสปอร์ตอิเล็กทรอนิกส์ กรุณาถือกล้องให้ตรง";
"FaceTec_idscan_nfc_status_weak_connection_message" = "กรุณาลองสแกนอีกครั้ง";
"FaceTec_idscan_nfc_status_finished_with_success_message" = "สแกนบัตรประชาชนสำเร็จ";
"FaceTec_idscan_nfc_status_finished_with_error_message" = "ระบบไม่สามารถอ่านชิพพาสปอร์ตอิเล็กทรอนิกส์";
"FaceTec_idscan_nfc_status_skipped_message" = "ข้ามขั้นตอนการสแกน NFC";

"FaceTec_instructions_header_ready_1" = "พร้อมแล้ว";
"FaceTec_instructions_header_ready_2" = "วีดิโอภาพใบหน้าของคุณ";
"FaceTec_instructions_message_ready_1" = "ขยับใบหน้าให้อยู่ในกรอบวงรี";
"FaceTec_instructions_message_ready_2" = "กดปุ่มฉันพร้อมแล้วและขยับใบหน้าให้ใกล้ขึ้น";

"FaceTec_presession_frame_your_face" = "กรุณาจัดรูปใบหน้าให้อยู่ในกรอบ";
"FaceTec_presession_position_face_straight_in_oval" = "มองตรงไปข้างหน้า";
"FaceTec_presession_hold_steady_3" = "นับถอยหลังก่อนถ่ายรูป วินาทีที่ 3";
"FaceTec_presession_hold_steady_2" = "นับถอยหลังก่อนถ่ายรูป วินาทีที่ 2";
"FaceTec_presession_hold_steady_1" = "นับถอยหลังก่อนถ่ายรูป วินาทีที่ 1";
"FaceTec_presession_eyes_straight_ahead" = "มองตรงไปข้างหน้า";
"FaceTec_presession_remove_dark_glasses" = "กรุณาถอดแว่นตาดำออก";
"FaceTec_presession_neutral_expression" = "กรุณาทำใบหน้าปกติ โดยไม่ต้องยิ้ม";
"FaceTec_presession_conditions_too_bright" = "สว่างเกินไป";
"FaceTec_presession_brighten_your_environment" = "กรุณาอยู่ในที่มีแสงสว่างกว่านี้";

"FaceTec_result_facescan_upload_message" = "กำลังอัพโหลดรูปภาพใบหน้า 3 มิติที่ผ่านการ Encrypted";
"FaceTec_result_success_message" = "ดำเนินการสำเร็จ";
"FaceTec_result_idscan_upload_message" = "กำลังอัพโหลดรูปภาพบัตรประชาชนที่ผ่านการ Encrypted";
"FaceTec_result_nfc_upload_message" = "กำลังอัพโหลดข้อมูล NFC ที่ผ่านการ Encrypted";
"FaceTec_result_idscan_unsuccess_message" = "รูปภาพใบหน้าไม่ตรงกับรูปบัตรประชาชน";
"FaceTec_result_idscan_success_front_side_message" = "สแกนหน้าบัตร\nเรียบร้อยแล้ว";
"FaceTec_result_idscan_success_front_side_back_next_message" = "สแกนหน้าบัตร\nเรียบร้อยแล้ว";
"FaceTec_initializing_camera" = "รักษาความปลอดภัย\nข้อมูลกล้อง";
"FaceTec_result_idscan_success_back_side_message" = "บันทึกรูปหลังบัตรประชาชนสำเร็จ";
"FaceTec_result_idscan_success_user_confirmation_message" = "การตรวจสอบหมายเลขบัตรประชาชนสำเร็จ";
"FaceTec_result_idscan_success_nfc_message" = "ข้อมูลในชิพพาสปอร์ตอิเล็กทรอนิกส์ผ่านการตรวจสอบแล้ว";
"FaceTec_result_idscan_skip_or_error_nfc_message" = "ข้อมูลในชิพพาสปอร์ตอิเล็กทรอนิกส์ถูกอัพโหลดสำเร็จ";
"FaceTec_result_idscan_retry_face_did_not_match_message" = "การเปรียบเทียบรูปภาพใบหน้าไม่สมบูรณ์พอ";
"FaceTec_result_idscan_retry_id_not_fully_visible_message" = "ภาพถ่ายบัตรประชาชนไม่ครบทั้งหมด";
"FaceTec_result_idscan_retry_ocr_results_not_good_enough_message" = "หมายเลขบัตรประชาชนบนบัตรไม่ชัดเจน";
"FaceTec_result_idscan_retry_id_type_not_supported_message" = "ระบบไม่รองรับบัตรแสดงตัวตนประเภทนี้ กรุณาใช้บัตรแสดงตัวตนอื่น";

"FaceTec_retry_header" = "กรุณาลองใหม่อีกครั้ง";
"FaceTec_retry_subheader_message" = "เราต้องการภาพถ่ายวีดิโอที่ชัดกว่านี้";
"FaceTec_retry_instruction_message_1" = "กรุณาทำใบหน้าปกติ โดยไม่ต้องยิ้ม";
"FaceTec_retry_instruction_message_2" = "ระวังแสงสะท้อนหรือแสงสว่างเกินไป";
"FaceTec_retry_instruction_message_3" = "รูปภาพไม่ชัด กรุณาทำความสะอาดกล้อง";
"FaceTec_retry_your_image_label" = "การถ่ายรูปภาพใบหน้า";
"FaceTec_retry_ideal_image_label" = "โพสต์ท่าให้เหมาะสม";

// MARK: - Ekyc review screen

// Title label
"Ekyc_review_main_title" = "ตรวจสอบและยืนยัน";
"Ekyc_review_header_title" = "ข้อมูล";
"Ekyc_review_title_name_en" = "คำนำหน้า\n(ภาษาอังกฤษ)*";
"Ekyc_review_title_name_th" = "คำนำหน้า*";
"Ekyc_review_first_name_en" = "ชื่อ\n(ภาษาอังกฤษ)*";
"Ekyc_review_first_name_th" = "ชื่อ*";
"Ekyc_review_middle_name_en" = "ชื่อกลาง\n(ภาษาอังกฤษ) (ถ้ามี)";
"Ekyc_review_middle_name_th" = "ชื่อกลาง (ถ้ามี)";
"Ekyc_review_last_name_en" = "นามสกุล\n(ภาษาอังกฤษ)*";
"Ekyc_review_last_name_th" = "นามสกุล*";
"Ekyc_review_card_id_number" = "เลขบัตรประจำตัวประชาชน*";
"Ekyc_review_laser_id_number" = "รหัสหลังบัตรประจำตัวประชาชน*";
"Ekyc_review_date_of_birth" = "วันเกิด\n(ระบุปี ค.ศ.)*";
"Ekyc_review_date_of_issue" = "วันที่ออกบัตร\n(ระบุปี ค.ศ.)*";
"Ekyc_review_date_of_expire" = "วันที่บัตรหมดอายุ\n(ระบุปี ค.ศ.)*";
"Ekyc_review_confirm" = "ยืนยันข้อมูล";

// Error message
"Ekyc_review_require" = "จำเป็น";
"Ekyc_review_expire_card" = "บัตรหมดอายุ";
"Ekyc_review_exceed_card" = "วันที่ออกบัตรต้องไม่มากกว่าวันที่ปัจจุบัน";
"Ekyc_review_allow_en" = "อนุญาตกรอกได้เฉพาะตัวอักษรภาษาอังกฤษ";
"Ekyc_review_allow_th" = "อนุญาตกรอกได้เฉพาะตัวอักษรภาษาไทย";
"Ekyc_review_allow_number" = "อนุญาตกรอกได้เฉพาะตัวเลข";
"Ekyc_review_allow_laser" = "รหัสหลังบัตรประจำตัวประชาชนต้องไม่น้อยกว่า 12 ตัวอักษร";
"Ekyc_review_allow_en_and_number" = "อนุญาตกรอกได้เฉพาะตัวอักษรภาษาอังกฤษพิมพ์ใหญ่หรือตัวเลข";
"Ekyc_review_dialog_description" = "กรุณาตรวจสอบข้อมูลก่อนทำการยืนยัน";
"Ekyc_review_confirm_dialog" = "ตกลง";
"Ekyc_review_cancel_dialog" = "ยกเลิก";
"Ekyc_review_date_of_birth_not_avaliable" = "กรุณากรอกข้อมูลให้ถูกต้อง\nถ้าไม่มีข้อมูลวันที่หรือเดือนเกิด กรุณาระบุ -";
"Ekyc_review_correct_error" = "กรุณากรอกข้อมูลให้ถูกต้อง";
"Ekyc_review_allow_card_id" = "กรุณาตรวจสอบเลขบัตรประชาชนอีกครั้ง";
"Ekyc_review_expire_within_seven_days" = "บัตรประจำตัวชาชนไม่สามารถใช้ได้ กรุณาทำบัตรประจำตัวประชาชนใหม่";
"Ekyc_review_check_sum_card_id" = "กรุณาตรวจสอบเลขบัตรประชาชนอีกครั้ง";

// Placeholder textfield
"Ekyc_review_title_name_th_placeholder" = "กรุณาระบุคำนำหน้า";
"Ekyc_review_first_name_th_placeholder" = "กรุณาระบุชื่อ";
"Ekyc_review_last_name_th_placeholder" = "กรุณาระบุนามสกุล";
"Ekyc_review_title_name_en_placeholder" = "กรุณาระบุคำนำหน้า (ภาษาอังกฤษ)";
"Ekyc_review_first_name_en_placeholder" = "กรุณาระบุชื่อ (ภาษาอังกฤษ)";
"Ekyc_review_last_name_en_placeholder" = "กรุณาระบุนามสกุล (ภาษาอังกฤษ)";
"Ekyc_review_card_id_number_placeholder" = "กรุณาระบุเลขบัตรประจำตัวประชาชน";
"Ekyc_review_laser_id_number_placeholder" = "กรุณาระบุรหัสหลังบัตรประจำตัวประชาชน";
"Ekyc_review_date_of_birth_placeholder" = "กรุณาระบุวันเกิด";
"Ekyc_review_date_of_issue_placeholder" = "กรุณาระบุวันที่ออกบัตร";
"Ekyc_review_date_of_expire_placeholder" = "กรุณาระบุวันที่บัตรหมดอายุ";
"Ekyc_review_no_expire_date_placeholder" = "บัตรตลอดชีพ";
"Ekyc_review_middle_name_th_placeholder" = "";
"Ekyc_review_middle_name_en_placeholder" = "";
"Ekyc_review_day_placeholder" = "วว";
"Ekyc_review_month_placeholder" = "ดด";
"Ekyc_review_year_placeholder" = "ปปปป";

// MARK: - Ekyc NDID IDP list screen

// Navigation title
"Ekyc_ndid_navigation_title" = "เปิดบัญชีลงทุน";

// IDP label
"Ekyc_ndid_idp_title" = "เลือกผู้ให้บริการยืนยันตัวตน (NDID)";
"Ekyc_ndid_idp_detail" = "กรุณาเลือกผู้ให้บริการยืนยันตัวตนที่คุณต้องการลงทะเบียนบริการ NDID (คุณต้องมีโมบายแอปพลิเคชันกับผู้ให้บริการยืนยันตัวตนดังกล่าวอยู่แล้ว)";
"Ekyc_ndid_idp_registered" = "ผู้ให้บริการยืนยันตัวตนที่เคยลงทะเบียนไว้ สามารถยืนยันตัวตนได้ทันที";
"Ekyc_ndid_idp_unregistered" = "ผู้ให้บริการยืนยันตัวตนอื่นที่คุณสามารถลงทะเบียนได้";
"Ekyc_ndid_idp_cancel" = "ยกเลิก";
"Ekyc_ndid_idp_next" = "ถัดไป";
"Ekyc_ndid_idp_error" = "คุณยังไม่เคยลงทะเบียนบริการ NDID กรุณาลงทะเบียน NDID กับผู้ให้บริการก่อน";

// MARK: - Ekyc NDID Holding screen

"Ekyc_ndid_holding_title" = "ยืนยันตัวตน (NDID)";
"Ekyc_ndid_holding_authenticate_title" = "กรุณายืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการยืนยันตัวตนที่คุณได้เลือกไว้";
"Ekyc_ndid_holding_authenticate_detail" = "ท่านกำลังยืนยันตัวตนเพื่อใช้ตามวัตถุประสงค์ของ SCBS และ ประสงค์ให้ส่งข้อมูลจาก %@";
"Ekyc_ndid_holding_verify_identification" = "กรุณาไปยืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการยืนยันตัวตนที่คุณเลือก ภายใน 60 นาที และกลับมาทำรายการต่อที่นี่";
"Ekyc_ndid_holding_open" = "เปิดแอป %@";
"Ekyc_ndid_holding_referralCode" = "Ref Code: %@";
"Ekyc_ndid_holding_cancel_button" = "ยกเลิกการยืนยันตัวตน";
"Ekyc_ndid_holding_confirm_cancel" = "คุณต้องการยกเลิกการยืนยันตัวตนใช่หรือไม่";
"Ekyc_ndid_holding_confirm" = "ยืนยันตัวตนเรียบร้อยแล้ว";
"Ekyc_ndid_holding_deeplink_alert" = "กรุณาไปยืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการที่คุณเลือก";
"Ekyc_ndid_holding_canceled_alert" = "คุณได้ยกเลิกรายการคำขอหรือเปลี่ยนผู้ให้บริการยืนยันตัวตน กรุณาเลือกผู้ให้บริการยืนยันตัวตน (Identity Provider) รายอื่น";
"Ekyc_ndid_holding_proceed_error" = "ขออภัย คุณไม่สามารถทำรายการได้ในขณะนี้ กรุณาทำรายการใหม่";
"Ekyc_ndid_holding_identification_error" = "คุณยังยืนยันตัวตนไม่สำเร็จ กรุณาทำรายการยืนยันตัวตนกับผู้ให้บริการยืนยันตัวตนที่เลือกไว้ให้สำเร็จ แล้วจึงกลับมาทำรายการเปิดบัญชีต่อโดยกดปุ่มนี้";
"Ekyc_ndid_holding_idp_reject" = "คุณได้ปฏิเสธการยืนยันตัวตนของผู้ให้บริการยืนยันตัวตนที่คุณเลือก กรุณาทำรายการใหม่หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น";

// alert button label

"Ekyc_ndid_cancel" = "ยกเลิก";
"Ekyc_ndid_next" = "ถัดไป";
"Ekyc_ndid_ok" = "ตกลง";
"Ekyc_ndid_yes" = "ใช่";
"Ekyc_ndid_no" = "ไม่ใช่";

// MARK: - Ekyc NDID Success screen

"Ekyc_ndid_successful_title" = "ยืนยันตัวตนเรียบร้อยแล้ว";
"Ekyc_ndid_successful_detail" = "ข้อมูลขอเปิดบัญชีได้ถูกส่งไปแล้ว";
"Ekyc_ndid_successful_referralCode" = "Ref Code: %@";
"Ekyc_ndid_successful_button" = "ถัดไป";

// MARK: - Ekyc Verified DOPA

"Ekyc_dopa_fail" = "คุณไม่สามารถเปิดบัญชีลงทุนได้ เนื่องจากข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลบัตรประชาชนใหม่และทำรายการใหม่อีกครั้ง";
"Ekyc_dopa_limit_reached" = "คุณไม่สามารถเปิดบัญชีลงทุนได้ กรุณาติดต่อสาขาธนาคารไทยพาณิชย์";

// MARK: - Ekyc OcrIdCard Enable camera screen

"Ekyc_camera_permission_header" = "เปิดใช้งานกล้อง";
"Ekyc_camera_permission_message_enroll" = "กรุณากดปุ่มด้านล่าง\nเพื่อเปิดใช้งานกล้องหน้า";
"Ekyc_camera_permission_enable_camera" = "เปิดใช้งานกล้อง";
