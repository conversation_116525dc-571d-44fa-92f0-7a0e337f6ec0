<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: ensure_git_status_clean" time="0.219404">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to get_release_notes lane" time="0.000229">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: git_pull" time="2.916215">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: last_git_tag" time="0.03544">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: Switch to ios_increment_build_number lane" time="0.000234">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="5: increment_build_number" time="12.983346">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="6: Switch to android_increment_build_number lane" time="0.000363">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="7: increment_version_code" time="0.016431">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="8: Switch to is_bump_clean lane" time="0.000216">
        
      </testcase>
    
  </testsuite>
</testsuites>
