
  def get_ios_google_app_id(environment)
    filename = "GoogleService-Info.#{environment.downcase}.plist"
    file_paths = Find.find('../').select { |p| p.include?(filename) }
    if file_paths.any?
      plist = File.expand_path(file_paths.first)
      app_id = get_info_plist_value(path: plist, key: 'GOOGLE_APP_ID')
    else
      puts "Could not find: #{filename}"
      return nil
    end
  end

  # TODO: Rename this to get-an
  # Gets the mobilesdk_app_id from the google-services.json
  #   depending on environment
  def get_android_google_app_id(environment)
    package_name_root = "com.techx.ekyc.demo"

    google_info_path = Dir["../android/app/src/#{environment}/google-services.json"][0]
    json_content = File.read(google_info_path)
    google_info = JSON.parse(json_content)
    
    clients = google_info['client']
    app_id = nil

    package_name_to_find = environment == 'prod' ? package_name_root : "#{package_name_root}.#{environment}" 
    
    clients.each { |client| 
      client_info = client['client_info']
      package_name = client_info['android_client_info']['package_name']
      puts package_name_to_find
      if package_name == package_name_to_find
        app_id = client_info['mobilesdk_app_id']
        break
      end
    }

    return app_id
  end

# Returns true or false if git status has correct number of files
lane :is_bump_clean do
  EXPECTED_FILE_COUNT = 12
  puts "Checking bump files"
  deleted_command = "git status | grep 'deleted:' | wc -l"
  untracked_command = "git status | grep 'Untracked' | wc -l"
  modified_command = "git status | grep 'modified:' | wc -l"
  num_deleted_files = `#{deleted_command}`.strip.to_i
  num_untracked_files = `#{untracked_command}`.strip.to_i
  num_modified_files = `#{modified_command}`.strip.to_i

  puts "Deleted file count: #{num_deleted_files}"
  puts "Untracked file count: #{num_untracked_files}"
  puts "Modified file count: #{num_modified_files}"

  if num_deleted_files > 0
    next false
  end

  if num_untracked_files > 0
    next false
  end

  if num_modified_files != EXPECTED_FILE_COUNT
    next false
  end

  true
end

# This lane takes an environment dev, dev2, dev3, dit, sit, uat, prod 
# Returns the distribution environment ("replaces dev2 with dev")
private_lane :get_distribute_env do |options|
  environment = options[:env]
  next if environment.nil? # skip doing the rest of this lane
  distributeEnv = environment.downcase 
  
  if distributeEnv.start_with? "dev"
    distributeEnv = "dev"
  end

  distributeEnv
end