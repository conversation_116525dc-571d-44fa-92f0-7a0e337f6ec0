fastlane documentation
================
# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```
xcode-select --install
```

Install _fastlane_ using
```
[sudo] gem install fastlane -NV
```
or alternatively using `brew install fastlane`

# Available Actions
### is_bump_clean
```
fastlane is_bump_clean
```

### upload_to_nexus
```
fastlane upload_to_nexus
```

### get_release_notes
```
fastlane get_release_notes
```
Depends on 2 environment variables for Jira authentication

JIRA_USERNAME and JIRA_PASSWORD must be set
### push_with_mr
```
fastlane push_with_mr
```
Pushes to git remote and creates an MR
### tag
```
fastlane tag
```
Tag for distribution
### bump
```
fastlane bump
```
Bump build numbers for android and ios
### upload_nexus
```
fastlane upload_nexus
```


----

## iOS
### ios build
```
fastlane ios build
```

### ios build_and_distribute
```
fastlane ios build_and_distribute
```

### ios build_ios
```
fastlane ios build_ios
```


----

## Android
### android build
```
fastlane android build
```

### android build_and_distribute
```
fastlane android build_and_distribute
```
Build and distribute to firebase

----

This README.md is auto-generated and will be re-generated every time [fastlane](https://fastlane.tools) is run.
More information about fastlane can be found on [fastlane.tools](https://fastlane.tools).
The documentation of fastlane can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
