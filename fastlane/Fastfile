require 'json'

import 'Helper.rb'
import 'Common.rb'
import 'Private.rb'
import 'NexusUpload'
import 'Jira.rb'

FULL_BUILD_PREFIX = 'builds/'

private_lane :get_release_notes_file do |options|
  is_release_notes_specified = options[:release_notes_file] != nil
  relative_notes_path = "../#{options[:release_notes_file]}"
  release_notes_file = is_release_notes_specified ? File.expand_path(relative_notes_path) : nil
end

desc "Pushes to git remote and creates an MR"
lane :push_with_mr do |options|
  push_options = ['merge_request.create']

  if options[:auto_merge] == true
    push_options = push_options.push('merge_request.merge_when_pipeline_succeeds')
  end
  
  if options[:target_branch] 
    push_options = push_options.push("merge_request.target=#{options[:target_branch].to_s}")
  end

  push_to_git_remote(push_options: push_options)
end

private_lane :get_tag do |options|
  # Get the build number of the Xcode project
  build_number = options[:build_number]
  tag = "#{FULL_BUILD_PREFIX}#{build_number}"
end

# fastlane tag [commit:{commit_hash}]
desc "Tag for distribution"
lane :tag do |options|
  commit = options[:commit]
  tag = options[:tag]

  next if tag.nil?

  # Tag the build
  add_git_tag(tag: tag , commit: commit)

  # Push tags to remote
  push_git_tags
end

desc 'android: increment versionCode'
private_lane :android_increment_build_number do |options|
  build_number = options[:build_number]

  increment_version_code(
    version_code: build_number,
    gradle_file_path: "./android/app/build.gradle",
  )
end

desc 'iOS: increment build number'
private_lane :ios_increment_build_number do |options| 
  build_number = options[:build_number] 

  increment_build_number(
    build_number: build_number,
    xcodeproj: './ios/ReactNativeBridgeIos.xcodeproj',
  )
end

desc 'Bump build numbers for android and ios'
lane :bump do |options|
  ensure_git_status_clean

  if options[:skip_release_notes] == true
  else
    get_release_notes
  end

  target_branch = options[:target_branch]
  build_number = options[:build_number]
  ios_build_number = ios_increment_build_number(build_number: build_number)
  android_build_number = android_increment_build_number(build_number: build_number)

  success = is_bump_clean
  
  if !success
    UI.error "Bump modified the incorrect number of files."
    next
  end

  tagLabel = get_tag(build_number: ios_build_number) # TODO: What if android and ios build numbers are different?
  
  if git_tag_exists(tag: tagLabel)
    reset_git_repo(skip_clean: true)
    UI.user_error!("Could not tag for: #{tagLabel}. Tag likely already exists.")
    next
  end
  
  # Commit build number bump up
  git_commit(path: ["./ios/**/*.plist", "./ios/*.xcodeproj/project.pbxproj", "./android/app/build.gradle"], message: "Bump up ios build number to #{ios_build_number} and android build number to #{android_build_number}")
  
  # Tag
  commit = last_git_commit
  tag(commit: commit[:commit_hash], tag: tagLabel) 

  push_with_mr(auto_merge: false, target_branch: target_branch)
end

lane :upload_nexus do |options|
  platform = options[:platform].downcase
  environment = options[:env].downcase
  next if environment.nil? # skip doing the rest of this lane

  distributeEnv = get_distribute_env(env: environment)

  # Nexus Upload
  upload_to_nexus(
    platform: platform,
    path_to_file: options[:path_to_file],
    env: distributeEnv,
  )
end

platform :ios do |options|
  before_all do
    xcversion(version: "15.0.1")
  end
  lane :build do |options|
    build_number = get_build_number(xcodeproj: './ios/ReactNativeBridgeIos.xcodeproj')
    env = options[:env].downcase
    # ipa filename format: EKYC-Mobile_dev_build-999.ipa
    ipa_filename = "#{"EKYC-Mobile"}_#{env}_build-#{build_number}.ipa"
    options[:output_name] = ipa_filename
    build_ios(options)
  end

  lane :build_and_distribute do |options|
    # release_notes_file = get_release_notes_file(release_notes_file: options[:release_notes_file])

    environment = options[:env].downcase
    # next if environment.nil? # skip doing the rest of this lane

    buildEnv = environment.downcase
    # distributeEnv = get_distribute_env(env: environment)

    app_id = get_ios_google_app_id(buildEnv)
    next if app_id.nil? 

    buildOptions = options.dup
    buildOptions[:env] = buildEnv

    path_to_ipa = build(buildOptions)
    groups = options[:groups] || ''
    
    firebase_app_distribution(
      app: app_id,
      groups: options[:firebase_tester_groups],
      firebase_cli_token: options[:firebase_cli_token],
      testers: options[:firebase_tester_emails],
      release_notes: options[:release_notes]
    )
  end

  lane :build_ios do |options|
    clean_build_artifacts
    clear_derived_data
    environment = options[:env]
    output_name = options[:output_name]
    scheme = environment.downcase == 'prod' ? 'EKYC-PROD' : 'EKYC-' + environment.upcase

    gym(
      workspace: 'ios/ReactNativeBridgeIos.xcworkspace',
      scheme: scheme,
      export_method: map_environment_to_export_method(environment: environment),
      export_xcargs: "-allowProvisioningUpdates",
      export_options: map_export_options(options),
      output_name: output_name
    )
  end
end

platform :android do

  lane :build do |options|
    environment = options[:env] || UI.select('Choose the environment: ', ['dev', 'sit', 'uat', 'prod'])

    next if environment.nil? # skip doing the rest of this lane
    environment = environment.downcase

    gradle(
      project_dir: './android/',
      task: "clean"
    )

    build_android_app(
      project_dir: './android/',
      task: "assemble",
      flavor: environment,
      build_type: "Release"
    )

    # Renaming the apk to format: EKYC-Mobile_dev_build-999.apk
    path_to_original_apk = "#{lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]}"
    output_path = path_to_original_apk.scan(/.*\//)[0].to_s
    new_apk_path = output_path + "#{"EKYC-Mobile"}_#{options[:env].downcase}_build.apk"
    sh("mv #{path_to_original_apk} #{new_apk_path}")
    new_apk_path
  end

  desc "Build and distribute to firebase"
  # fastlane distribute [env:(dev|dit|sit|uat)]
  lane :build_and_distribute do |options|
    # release_notes_file = get_release_notes_file(release_notes_file: options[:release_notes_file])
    
    environment = options[:env]
    
    buildEnv = environment.downcase
    # distributeEnv = get_distribute_env(env: environment)
    
    app_id = get_android_google_app_id(buildEnv)
    next if app_id.nil? 
    
    buildOptions = options.dup
    buildOptions[:env] = buildEnv
    
    apk_path = build(buildOptions)

    print 'Uploading to Firebase...'
    
    groups = options[:groups] || ''

    firebase_app_distribution(
      app: app_id,
      groups: options[:firebase_tester_groups],
      apk_path: apk_path,
      firebase_cli_token: options[:firebase_cli_token],
      testers: options[:firebase_tester_emails],
      release_notes: options[:release_notes]
    )
    
  end
end
