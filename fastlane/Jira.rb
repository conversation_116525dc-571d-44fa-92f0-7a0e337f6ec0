#require 'jira-ruby'

def get_jira_client() 
    username = <PERSON>NV["JIRA_USERNAME"]
    password = ENV["JIRA_PASSWORD"]
    if username.nil? || password.nil?
        UI.user_error!("Could not create Jira client. \n Make sure environment variables are set for JIRA_USERNAME and JIRA_PASSWORD")
        return
    end

    options = {
        :username     => username,
        :password     => password,
        :site         => 'https://jira.easy2easiest.com',
        :context_path => '',
        :auth_type    => :cookie,
        :use_cookies  => true,
    }
    return JIRA::Client.new(options)
end

desc "Depends on 2 environment variables for Jira authentication"
desc "JIRA_USERNAME and JIRA_PASSWORD must be set"
lane :get_release_notes do |options|
    git_pull(only_tags: true)
    last_tag = last_git_tag(pattern: "builds/*")
    
    next if last_tag.nil?
    
    UI.message "Getting release notes between HEAD and #{last_tag}"
    changelog = changelog_from_git_commits(
        between: [last_tag, "HEAD"],
        merge_commit_filtering: "only_include_merges",
        quiet: true
    )
    
    next if changelog.nil?
    
    # Compose release notes 
    release_notes = options[:prefix] || "Tickets (Frontend):"
    release_notes ="#{release_notes}\n"

    client = get_jira_client()
    matches = changelog.scan(/([A-Z]{1,10}-\d+)/)
    
    tickets = {}

    if matches.length == 0 
        UI.error "No Jira tickets found"
    end

    matches.each { |match| 
        issue_key = match[0].to_s
        next if !tickets[issue_key].nil? 
        issue = client.Issue.find(issue_key)
        tickets[issue_key] = issue.summary
        release_notes += "- #{issue_key}: #{issue.summary}\n"
    }

    UI.success release_notes

    clipboard(value: release_notes)
end

