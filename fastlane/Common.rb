import 'NexusUpload'

BASE_REPO_GROUP_ID = 'com.ekyc.demo'

lane :upload_to_nexus do |options|
    platform = options[:platform] # android, ios

    file_extension = platform == 'ios' ? 'ipa' : 'apk'

    `which mvn`
    if $?.success?
        path_to_file = options[:path_to_file] || Dir.glob("../**/*.#{file_extension}").sort_by { |x| File.mtime(x) }.map(&File.method(:realpath)).last
        next if path_to_file.nil? # skip doing the rest of this lane
        puts "path_to_file = #{path_to_file}"

        build_number = options[:build_number] || File.basename(path_to_file, ".#{file_extension}").match(/CPX-Mobile_(.*)/)[1] #ipa name pattern: CPX-Mobile_dev130_build-999.ipa
        puts "build_number = #{build_number}"
        next if build_number.nil? # skip doing the rest of this lane
        repo_id = options[:env] == 'prod' ? 'mobile-releases' : 'mobile-snapshots'
        repo_group_id = "#{BASE_REPO_GROUP_ID}.#{platform}"
        puts "repo_group_id = #{repo_group_id}"

        NexusUpload.run(
            packaging: file_extension,
            file: path_to_file,
            repo_id: repo_id,
            repo_group_id: repo_group_id,
            repo_project_name: 'SCB-CPXMB',
            repo_project_version: build_number,
            repo_classifier: "", # Optional
            endpoint: 'https://nexus.devops.easy2easiest.com',
        )
    else
      UI.user_error!('ERROR: mvn command not found or does not exist. To install maven, please execute `brew install maven`')
    end
end