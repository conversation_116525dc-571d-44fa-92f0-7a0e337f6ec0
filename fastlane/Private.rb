private_lane :map_environment_to_export_method do |options|
  #Only enterprise
  'enterprise'
  # if options[:environment].nil?
  #   'enterprise'
  # else
  #   (options[:environment].downcase == 'prod' ? 'app-store' : 'enterprise')
  # end
end

private_lane :map_export_options do |options|
  if options[:env].nil? || options[:env].downcase != 'prod'
  {
    compileBitcode: false,
    provisioningProfiles: {
      "com.techx.ekyc.demo.dev" => "match InHouse com.techx.ekyc.demo.dev",
      "com.techx.ekyc.demo.sit" => "match InHouse com.techx.ekyc.demo.sit",
      "com.techx.ekyc.demo.uat" => "match InHouse com.techx.ekyc.demo.uat",
      "com.techx.ekyc.demo.prod" => "match InHouse com.techx.ekyc.demo",
    },
    signingCertificate: "iPhone Distribution",
    signingStyle: "manual",
    stripSwiftSymbols: true,
    teamID: "WF47643S5X",
    thinning: "<none>"
  }
  # else
  # {
  #   compileBitcode: false,
  #   uploadBitcode: false,
  #   provisioningProfiles: {
  #     "com.scb.corporate" => "CORPORATE PROD DISTRIBUTION"
  #   },
  #   signingCertificate: "iPhone Distribution",
  #   signingStyle: "manual",
  #   stripSwiftSymbols: true,
  #   teamID: "4M6326LS95",
  #   uploadSymbols: true
  # }
  end
end