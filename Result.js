import React, { Component } from 'react';
import { StyleSheet, Text, View, NativeModules, Button, Platform } from 'react-native';

export default class Result extends Component {

    render(){
        const { params } = this.props.navigation.state;
        return(
        <View style={styles.container}>
            <Text selectable={true} style={{ fontSize: 18 ,color: 'black' }}>{params.data}</Text>
        </View>
        );
    }

}

const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#fffff',
      alignItems: 'center',
      justifyContent: 'center',
    },
  });