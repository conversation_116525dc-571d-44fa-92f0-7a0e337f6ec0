import React, { Component, useState } from 'react';
import { StyleSheet, Image, ImageBackground, Text, View, NativeModules, TextInput, Platform, Pressable, Alert, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { AlertWithTextModal } from './components/AlertWithTextModal';
import { AlertWithYesNoModal } from './components/AlertWithYesNoModal'
import { AlertWithSelectModal } from './components/AlertWithSelectModal'
const { OpenAndroidModule } = NativeModules
const { Connect } = NativeModules;
import styles from './styles';

const Features = {
	Liveness: 0,
	OCRIdCardVerifyByFace: 1,
	NDID: 2,
    OCROnly: 3
}

class EkycHandler {
    static token = ""
    static checkDopa = false
    static checkExpiredIdCard = false
    static enableConfirmInfo = false
    static prefill = ""

    static async setToken(data) {
        this.token = data;
    };

    static setCheckDopa(data) {
        this.checkDopa = data;
    };

    static setCheckExpiredIdCard(data) {
        this.checkExpiredIdCard = data;
    };

    static setEnableConfirmInfo(data) {
        this.enableConfirmInfo = data;
    };

    static setPrefill(data) {
        this.prefill = data;
    };

    static openOcrIdCardVerifyByFace() {
        Platform.OS === 'ios' ? Connect.goToOcrIdCardVerifyByFace(this.token, this.checkDopa, this.checkExpiredIdCard, this.enableConfirmInfo, this.prefill) :
                            OpenAndroidModule.ocrIdScanVerifyByFace(
                                this.checkDopa,
                                this.checkExpiredIdCard,
                                this.enableConfirmInfo,
                                this.token,
                                this.prefill
                            );
    };

    static openOcrOnly() {
        Platform.OS === 'ios' ? Connect.goToOcrIdCard(this.token, this.checkDopa, this.checkExpiredIdCard, this.enableConfirmInfo, this.prefill) :
                            OpenAndroidModule.ocrIdCard(
                                this.checkDopa,
                                this.checkExpiredIdCard,
                                this.enableConfirmInfo,
                                this.token,
                                this.prefill
                            );
    }

    static openLiveness() {
        Platform.OS === 'ios' ? Connect.goToLiveness(this.token) : OpenAndroidModule.livenessCheck(this.token)
    };

    static openNdid(cidNumber) {
        Platform.OS === 'ios' ? Connect.goToNdid(this.token, cidNumber) :
                            OpenAndroidModule.ndidVerification(cidNumber, this.token);
    };
}

export default class Home extends Component {
    constructor(props) {
        super(props);
    }

    state = {
        showCid: false,
        showDopaPick: false,
        showExpirePick: false,
        showEnableConfirmPick: false,
        showTokenField: false,
        showPrefill: false,
        pickedFeature: 0
    };

    showCidDialog(data) {
        this.setState({showCid: data});
    };

    showDopaPickDialog(data) {
        this.setState({showDopaPick: data});
    };

    showExpireIDDialog(data) {
        this.setState({showExpirePick: data});
    };

    showEnableConfirmDialog(data) {
        this.setState({showEnableConfirmPick: data});
    };

    showPrefillDialog(data) {
        this.setState({showPrefill: data});
    };

    showTokenField(show, feature) {
        this.setState({showTokenField: show});
        this.setState({pickedFeature: feature});
    };

    getCidInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showCid} transparent={true} >
                <AlertWithTextModal 
                    title={"Please input CID number"} 
                    placeholder={"13 numbers or empty"}
                    onConfirm={ (cidText) => {
                        EkycHandler.openNdid(cidText)
                        this.showCidDialog(false);
                    }
                    }
                    onCancel={ () => {
                        this.showCidDialog(false);
                    }
                    }
                />
            </Modal>
        );
    };

    getTokenInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showTokenField} transparent={true} >
                <AlertWithTextModal 
                    title={"Please input token"} 
                    placeholder={""}
                    onConfirm={ async (token) => {
                        await EkycHandler.setToken(token)
                        if (this.state.pickedFeature == Features.NDID) {
                            this.showCidDialog(true)
                        }
                        else if (this.state.pickedFeature == Features.Liveness) {
                            EkycHandler.openLiveness()
                        }
                        else if (this.state.pickedFeature == Features.OCRIdCardVerifyByFace) {
                            this.showDopaPickDialog(true)
                        } else if (this.state.pickedFeature == Features.OCROnly) {
                            this.showDopaPickDialog(true)
                        }
                        this.showTokenField(false, this.state.pickedFeature);
                    }
                    }
                    onCancel={ () => {
                        this.showTokenField(false);
                    }
                    }
                />
            </Modal>
        );
    };

    getDopaInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showDopaPick} transparent={true} >
                <AlertWithYesNoModal
                    title={"Check Dopa"}
                    onPickChoice={ (pickedChoice) => {
                        this.showDopaPickDialog(false);
                        this.showExpireIDDialog(true);
                        EkycHandler.setCheckDopa(pickedChoice);
                    }}
                />
            </Modal>
        );
    };

    getExpireIdInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showExpirePick} transparent={true} >
                <AlertWithYesNoModal
                    title={"Check Expire ID"}
                    onPickChoice={ (pickedChoice) => {
                        EkycHandler.setCheckExpiredIdCard(pickedChoice);
                        this.showExpireIDDialog(false);
                        this.showEnableConfirmDialog(true);
                    }}
                />
            </Modal>
        );
    };

    getEnableConfirmInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showEnableConfirmPick} transparent={true} >
                <AlertWithYesNoModal
                    title={"Enable Confirm Info"}
                    onPickChoice={ (pickedChoice) => {
                        EkycHandler.setEnableConfirmInfo(pickedChoice)
                        this.showEnableConfirmDialog(false);
                        this.showPrefillDialog(true)
                    }}
                />
            </Modal>
        );
    };

    getPrefillInputPopup = () => {
        // You can include logic or dynamic content here
        return (
            <Modal visible={this.state.showPrefill} transparent={true} >
                <AlertWithSelectModal
                    title={"Prefill"}
                    onPickChoice={ (prefillData) => {
                        EkycHandler.setPrefill(prefillData);
                        if (this.state.pickedFeature === Features.OCRIdCardVerifyByFace) {
                            EkycHandler.openOcrIdCardVerifyByFace();
                        } else if (this.state.pickedFeature === Features.OCROnly) {
                            EkycHandler.openOcrOnly();
                        }
                        this.showPrefillDialog(false);
                    }}
                />
            </Modal>
        );
    };

    render() {
        const { navigate } = this.props.navigation;
        return (
            <ImageBackground source={require('./images/BG.jpg')} resizeMode="cover" style={styles.image}>
                <SafeAreaView style={styles.container}>
                    {this.getTokenInputPopup()}
                    {this.getCidInputPopup()}
                    {this.getDopaInputPopup()}
                    {this.getExpireIdInputPopup()}
                    {this.getEnableConfirmInputPopup()}
                    {this.getPrefillInputPopup()}
                    <View style={styles.top}>
                        <Image source={require('./images/logo_techx_img.png')} style={styles.techxLogo}/>
                        <Image source={require('./images/label_techx_img.png')} style={styles.techxLabel}/>
                        <Text style={{ fontSize: 16, color: 'white' }}>This Page is React Native by App.js</Text>
                    </View>
                    <View style={styles.center}>
                        <Pressable style={({ pressed }) => [
                            {
                                backgroundColor: pressed ? "transparent" : "transparent", 
                            },
                            styles.normalView
                            ]} 
                            onPress={() =>
                                this.showTokenField(true, Features.OCRIdCardVerifyByFace)
                            }
                        >
                            <Text style={styles.normalViewText}>Ocr Id Card Verify By Face Screen</Text>
                        </Pressable>
                        <Pressable style={({ pressed }) => [
                            {
                                backgroundColor: pressed ? "transparent" : "transparent", 
                            },
                            styles.normalView
                            ]} 
                            onPress={() =>
                                this.showTokenField(true, Features.Liveness)
                            }
                        >
                            <Text style={styles.normalViewText}>Liveness Screen</Text>
                        </Pressable>
                        <Pressable style={({ pressed }) => [
                            {
                                backgroundColor: pressed ? "transparent" : "transparent",
                            },
                            styles.normalView
                            ]}
                            onPress={() => this.showTokenField(true, Features.NDID)
                            }
                        >
                            <Text style={styles.normalViewText}>Ndid Screen</Text>
                        </Pressable>
                        <Pressable style={({ pressed }) => [
                            {
                                backgroundColor: pressed ? "transparent" : "transparent",
                            },
                            styles.normalView
                            ]}
                            onPress={() => this.showTokenField(true, Features.OCROnly)
                            }
                        >
                            <Text style={styles.normalViewText}>OCR Only Screen</Text>
                        </Pressable>
                    </View>
                    <View style={styles.bottom}>
                        <Pressable style={({ pressed }) => [
                            {
                                backgroundColor: pressed ? "transparent": "transparent",  
                            },
                            styles.outlineView
                            ]} 
                            onPress={() =>
                                Platform.OS === 'android' ?
                                    OpenAndroidModule.doCallBackTask(
                                        (result) => {
                                            navigate('Result', { data: result })
                                        }
                                    ) : 
                                    Connect.callbackData(
                                        (result) => {
                                            navigate('Result', { data: result })
                                        }
                                    )
                            }
                        >
                            <Text style={styles.outlineViewText}>Press to Show Result</Text>
                        </Pressable>
                    </View>
                </SafeAreaView>
            </ImageBackground>
        );
    }
}