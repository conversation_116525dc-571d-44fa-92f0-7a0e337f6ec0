import react from "react";
import React, { Component, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  Pressable,
  Modal,
  ScrollView,
  Switch,
} from "react-native";
import styles from "../styles";
import { SelectList } from "react-native-dropdown-select-list";
import { DropDownInputField } from './DropDownInputField'

const AlertWithSelectModal = (props) => {
  clickConfirm = (data) => {
    props.onPickChoice(data);
  };

  const [isEnabled, setIsEnabled] = useState(false);

  const [prefill, setPrefill] = useState({
    titleThFlag: "null",
    titleEnFlag: "null",
    firstNameThFlag: "null",
    firstNameEnFlag: "null",
    lastNameThFlag: "null",
    lastNameEnFlag: "null",
    dateOfBirthFlag: "null",
    dateOfIssueFlag: "null",
    dateOfExpiryFlag: "null",
    laserIdFlag: "null",
    isPrefillSet: false
  });

  const toggleSwitch = () => {
    setIsEnabled((previousState) => !previousState);
    setPrefill({
      ...prefill,
      isPrefillSet: !isEnabled,
    })
  };

  const data = [
    { key: "1", value: "null" },
    { key: "2", value: "true" },
    { key: "3", value: "false" },
  ];

  return (
    <View style={styles.transparentBlackBackground}>
      <View style={styles.whiteBackground}>
        <View style={styles.row}>
          <Switch
            trackColor={{ false: "#767577", true: "#81b0ff" }}
            thumbColor={isEnabled ? "#f5dd4b" : "#f4f3f4"}
            ios_backgroundColor="#3e3e3e"
            onValueChange={toggleSwitch}
            value={isEnabled}
          />
          <Text>{props.title}</Text>
        </View>
        {isEnabled && (
          <ScrollView style={styles.scrollPopup}>
            <DropDownInputField 
              title="titleThFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  titleThFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="titleEnFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  titleEnFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="firstNameThFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  firstNameThFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="firstNameEnFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  firstNameEnFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="lastNameThFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  lastNameThFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="lastNameEnFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  lastNameEnFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="dateOfBirthFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  dateOfBirthFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="dateOfIssueFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  dateOfIssueFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="dateOfExpiryFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  dateOfExpiryFlag: val,
                })
              }}
            />
            <DropDownInputField 
              title="laserIdFlag"
              onSelect={ (val) => {
                setPrefill({
                  ...prefill,
                  laserIdFlag: val,
                })
              }}
            />
            <View style={styles.container}/>
          </ScrollView>
        )}
        <View style={styles.space}></View>
        <View style={styles.space}></View>
        <View style={styles.row}>
          <Pressable
            style={({ pressed }) => [
              {
                backgroundColor: pressed ? "#3742fa" : "#007AFF",
              },
              styles.largeButtonView,
            ]}
            onPress={() => clickConfirm(prefill)}
          >
            <Text style={styles.smallButtonText}>OK</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

export { AlertWithSelectModal };
