import react from "react";
import React, { Component, useState } from 'react';
import { StyleSheet, Text, View, TextInput, Pressable, Modal } from 'react-native';
import styles from '../styles';

const AlertWithYesNoModal = (props) => {
    pickAnswer = (data) => {
        props.onPickChoice(data);
    }
    return (
        <View style={styles.transparentBlackBackground}>
            <View style={styles.whiteBackground}>
                <Text>{props.title}</Text>
                <View style={styles.row}>
                    <Pressable style={({ pressed }) => [
                                {
                                    backgroundColor: pressed ? "#3742fa" : "#007AFF",
                                },
                                styles.smallButtonLeftView
                            ]}
                            onPress={() => {
                                pickAnswer(true);
                            }
                        }
                    >
                        <Text style={styles.smallButtonText}>true</Text>
                    </Pressable>
                    <Pressable style={({ pressed }) => [
                                {
                                    backgroundColor: pressed ? "#3742fa" : "#007AFF",
                                },
                                styles.smallButtonRightView
                            ]
                        }
                            onPress={() => {
                                pickAnswer(false);
                            }
                        }
                    >
                        <Text style={styles.smallButtonText}>false</Text>
                    </Pressable>
                </View>
            </View>
        </View>
    );
};

export {AlertWithYesNoModal}