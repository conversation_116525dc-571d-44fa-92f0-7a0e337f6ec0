import react from "react";
import React, { Component, useState } from 'react';
import { StyleSheet, Text, View, TextInput, Pressable, Modal } from 'react-native';
import styles from '../styles';

const AlertWithTextModal = (props) => {
    clickConfirm = (data) => {
        props.onConfirm(data);
    }
    clickCancel = () => {
        props.onCancel();
    }
    const [inputText, onChangeText] = React.useState("");
    return (
        <View style={styles.transparentBlackBackground}>
            <View style={styles.whiteBackground}>
                <Text>{props.title}</Text>
                <TextInput
                    style={styles.input}
                    onChangeText={onChangeText}
                    editable
                    placeholder={props.placeholder}
                    placeholderTextColor="#00000044"
                    value={inputText}
                />
                <View style={styles.row}>
                    <Pressable style={({ pressed }) => [
                        {
                            backgroundColor: pressed ? "#3742fa" : "#007AFF",
                        },
                        styles.smallButtonLeftView
                        ]}
                        onPress={() => clickConfirm(inputText)}
                    >
                        <Text style={styles.smallButtonText}>OK</Text>
                    </Pressable>
                    <Pressable style={({ pressed }) => [
                        {
                            backgroundColor: pressed ? "#3742fa" : "#007AFF",
                        },
                        styles.smallButtonRightView
                        ]}
                        onPress={() => clickCancel()}
                    >
                        <Text style={styles.smallButtonText}>Cancel</Text>
                    </Pressable>
                </View>
            </View>
        </View>
    );
};

export {AlertWithTextModal}