import react from "react";
import React, { Component, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  Pressable,
} from "react-native";
import styles from "../styles";
import { SelectList } from "react-native-dropdown-select-list";

const DropDownInputField = (props) => {
    const data = [
        { key: "1", value: "null" },
        { key: "2", value: "true" },
        { key: "3", value: "false" },
    ];
    
    const abc = (val) => {
        props.onSelect(val);
    }
      
    return (
        <View>
            <Text>{props.title}</Text>
            <SelectList
                setSelected={(val) => {abc(val)}}
                data={data}
                defaultOption={{ key:'1', value:'null' }}
                save="value"
                searchPlaceholder="Select"
            />
            <View style={styles.space}/>
        </View>
    )
  }

  export { DropDownInputField };