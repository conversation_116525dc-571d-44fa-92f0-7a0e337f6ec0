# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Increased heap size to handle large builds and DEX merging
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true

# Flipper removed as it's deprecated in React Native 0.73+
# FLIPPER_VERSION=0.99.0

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# Android. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use TurboModules/Fabric
# libraries. If you don't plan to use TurboModules/Fabric, then this is a no-op
# and your app will build and work as it did before.
newArchEnabled=false

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Suppress Android Gradle Plugin warning for compileSdk 35
android.suppressUnsupportedCompileSdk=35

# Fix Kotlin Gradle plugin BuildFlowService issues
kotlin.incremental=false
kotlin.compiler.execution.strategy=in-process

# Fix bcprov and dependency resolution issues
org.gradle.unsafe.configuration-cache=false
org.gradle.configuration-cache=false
org.gradle.caching=false

# Disable Gradle build scan and statistics
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000
