package com.techx.ekyc.demo;

import android.content.Context;
import android.os.Build;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue;
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult;
import com.scb.techx.ekycframework.ui.theme.CustomizeTheme;
import com.scb.techx.ekycframework.ui.theme.Image;
import com.scb.techx.ekycframework.ui.model.ocrprefill.OcrPrefill;
import com.scb.techx.ekycframework.util.EkycUtilities;

//TODO: DEL THESE LATER
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import android.widget.Toast;

import java.util.Locale;
import java.util.UUID;

class OpenAndroidModule extends ReactContextBaseJavaModule {

    String token = "";
    String devToken = "INSERT YOUR DEV TOKEN HERE";
    String sitToken = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    String uatToken = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    String preprodToken = "eyJraWQiOiJzY2J0ZWNoeCIsInR5cCI6IkpXVCIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************.OgFh-d0bFSOpADm99iiMeFbUNdU8GFeupewBeY8irYaqPYacGlUxDwfAUKj4W30txBZceQpc7KEfXnuaENYxpnvNFSyER2NZKqG40bLNy9p3hz-qeLCtdm_Lw5T0DLCn9oUdi0O_-oDkqeYYHnPDp8Q9FJEBK5zZAMZ7vjzrf1aPJ3SKvJD6CF8_5aaFyXckc-9WK0JaHgrBYTWoh3FHVI_MpXD2MDtZRgUTn8ssfpAxHw6gTLkTXejhwTBfmTMECWVgBcaNt72qkp8snf8bMfOmTbgDRllwT5u8ZrcGY7OYUD5_QTVv0jkf2oOi6T0DUIS2N4isssvdQTje-cJlLHxrS76FjDpazx4gAE4iFQkG1DpgQIhey041mMfq1PYmL2sTXAuKTvZkW5aYe3t9Y84grrqGqKSZx5udUCpJOtECkINHShxR4id3atMemCF6zysH2F0JMJPf0U0245HR2ztNQv6Ux3JBMJw2QORGH3vfzbCxf1_HbpRnoQN5ZA0iSK58tpuRgKQ8_2bm5k7XsLns3p0plyghCfPOIXURI7tls2xKbv8ztvYfck0SY3fTVRbB88z1CQ2MtSAUj0Z2Dd1PB2psT3PiF7qaoNwX-JFaWLHhYktPHhd9LDHUqzwOnNX5_IuyJzido5AZGw6vACBOgZP9chuekmvVc6KeUXE";
    String prodToken = "INSERT YOUR PROD TOKEN HERE";
    String sessionId = "7e4f518d-f4ae-447a-a6e4-d4143135e7b1";
    ReactContext mReactContext;
    String resultString = null;
    String ekycToken = "";
    EkycUtilities ekycUtilities = new EkycUtilities();

    public OpenAndroidModule(ReactApplicationContext reactContext) {
        super(reactContext);
        mReactContext = reactContext;
    }

    private static final String MODULE_NAME = "OpenAndroidModule";

    @NonNull
    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @ReactMethod
    public void doCallBackTask(Callback callback) {
        if (resultString != null) {
            callback.invoke(resultString);
        } else {
            callback.invoke("No Result");
        }
    }

    public void setToken(String inputToken) {
        if (inputToken.length() != 0) {
            token = inputToken;
        }
        else {
            if (BuildConfig.FLAVOR.toUpperCase().equals("SIT")) {
                token = sitToken;
            } else if (BuildConfig.FLAVOR.toUpperCase().equals("UAT")) {
                token = uatToken;
            }
            else if (BuildConfig.FLAVOR.toUpperCase().equals("DEV")) {
                token = devToken;
            }
            else if (BuildConfig.FLAVOR.toUpperCase().equals("PREPROD")) {
                token = preprodToken;
            }
            else if (BuildConfig.FLAVOR.toUpperCase().equals("PROD")) {
                token = prodToken;
            }
        }
    }

    public void setSessionId() {
        this.sessionId = UUID.randomUUID().toString();
    }

    @ReactMethod
    public void livenessCheck(String inputToken) {
        setSessionId();
        setToken(inputToken);
        // Example Customize Theme
        // Typeface font = ResourcesCompat.getFont(mReactContext.getCurrentActivity(), R.font.uranus);
        // Text textTheme = new Text(font, "#000000", "#000000", "#FFFFFF", null);
        // Image image = new Image(R.drawable.techx, null, null, null, null, null);
        // CustomizeTheme customizeTheme = new CustomizeTheme(textTheme, null, null, null, image, null, null, null);
        Image image = new Image(R.drawable.techx_logo, null, null, null, null, null);
        CustomizeTheme customizeTheme = new CustomizeTheme(null, null, null, null,image, null , null, null);

        ekycUtilities.initEkyc(
                getReactApplicationContext().getApplicationContext(),
                mReactContext,
                this.sessionId,
                this.token,
                BuildConfig.FLAVOR.toUpperCase(),
                customizeTheme,
                null,
                null,
                (success, description, ekycToken) -> {
                    this.ekycToken = ekycToken;
                    if (success) {
                        ekycUtilities.livenessCheck(
                                mReactContext.getCurrentActivity(),
                                (livenessSuccess, livenessDescription) -> {
                                    saveOtherResultString(livenessSuccess, livenessDescription);
                                }
                        );
                    } else {
                        saveOtherResultString(success, description);
                    }
                }
        );
    }

    @ReactMethod
    public void ndidVerification(String cidText, String inputToken) {
        setSessionId();
        setToken(inputToken);
        
        final String cid;

        if (cidText == null || cidText.length() == 0) {
            cid = "1309913659936";
        } else {
            cid = cidText;
        }

        // Example Customize Theme
        // Typeface font = ResourcesCompat.getFont(mReactContext.getCurrentActivity(), R.font.uranus);
        // Text textTheme = new Text(font, "#000000", "#000000", "#FFFFFF", null);
        // Image image = new Image(R.drawable.techx, null, null, null, null, null);
        // CustomizeTheme customizeTheme = new CustomizeTheme(textTheme, null, null, null, image, null, null, null);
         Image image = new Image(null, null, null, null, null, null);
         CustomizeTheme customizeTheme = new CustomizeTheme(null, null, null, null, image, null, null, null);
        ekycUtilities.initEkyc(
                getReactApplicationContext().getApplicationContext(),
                mReactContext,
                this.sessionId,
                this.token,
                BuildConfig.FLAVOR.toUpperCase(),
                customizeTheme,
                null,
                null,
                (success, description, ekycToken) -> {
                    this.ekycToken = ekycToken;
                    if (success) {
                        ekycUtilities.ndidVerification(mReactContext.getCurrentActivity(), "national_id",
                                cid, "001.cust_info_001", ((ndidSuccess, ndidDescription, ndidStatus, ndidError, ndidData) -> {
                                        saveNdidResultString(ndidSuccess, ndidDescription, ndidStatus, ndidError, ndidData);
                                    }
                                )
                        );
                    } else {
                        saveOtherResultString(success, description);
                    }
                }
        );
    }

    @ReactMethod
    public void ocrIdScanVerifyByFace(Boolean checkDopa, Boolean checkExpiredIdCard, Boolean enableConfirmInfo, String inputToken, ReadableMap ocrPrefill) {
        setSessionId();
        setToken(inputToken);
        // Example Customize Theme
        // Typeface font = ResourcesCompat.getFont(mReactContext.getCurrentActivity(), R.font.uranus);
        // Text textTheme = new Text(font, "#000000", "#000000", "#FFFFFF");
        // Image image = new Image(R.drawable.techx, null, null, null, null, null);
        // CustomizeTheme customizeTheme = new CustomizeTheme(textTheme, null, null, null, image, null);
        Image image = new Image(R.drawable.techx_logo, null, null, null, null, null);
        CustomizeTheme customizeTheme = new CustomizeTheme(null, null, null, null, image, null, null, null);
        Boolean isPrefillSet = ocrPrefill.getBoolean("isPrefillSet");

        OcrPrefill prefill = new OcrPrefill(
            mapFromStringToBoolean(ocrPrefill.getString("titleThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("titleEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("firstNameThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("firstNameEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("lastNameThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("lastNameEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfBirthFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfIssueFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfExpiryFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("laserIdFlag"))
        );

        ekycUtilities.initEkyc(
                getReactApplicationContext().getApplicationContext(),
                mReactContext,
                this.sessionId,
                this.token,
                BuildConfig.FLAVOR.toUpperCase(),
                customizeTheme,
                null,
                null,
                (success, description, ekycToken) -> {
                    this.ekycToken = ekycToken;
                    if (success) {
                        ekycUtilities.ocrIdCardVerifyByFace(
                                mReactContext.getCurrentActivity(),
                                checkExpiredIdCard,
                                checkDopa,
                                enableConfirmInfo,
                                (isPrefillSet)? prefill : null,
                                (ocrSuccess, ocrDescription, userOcrValue, userConfirmedValue, dopaResult) -> {
                                    saveOcrResultString(ocrSuccess, ocrDescription, userOcrValue, userConfirmedValue, dopaResult);
                                }
                        );
                    } else {
                        saveOtherResultString(success, description);
                    }
                }
        );
    }

    @ReactMethod
    public void ocrIdCard(Boolean checkDopa, Boolean checkExpiredIdCard, Boolean enableConfirmInfo, String inputToken, ReadableMap ocrPrefill) {
        setSessionId();
        setToken(inputToken);
        // Example Customize Theme
        // Typeface font = ResourcesCompat.getFont(mReactContext.getCurrentActivity(), R.font.uranus);
        // Text textTheme = new Text(font, "#000000", "#000000", "#FFFFFF");
        // Image image = new Image(R.drawable.techx, null, null, null, null, null);
        // CustomizeTheme customizeTheme = new CustomizeTheme(textTheme, null, null, null, image, null);
        Image image = new Image(R.drawable.techx_logo, null, null, null, null, null);
        CustomizeTheme customizeTheme = new CustomizeTheme(null, null, null, null, image, null, null, null);
        Boolean isPrefillSet = ocrPrefill.getBoolean("isPrefillSet");

        OcrPrefill prefill = new OcrPrefill(
            mapFromStringToBoolean(ocrPrefill.getString("titleThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("titleEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("firstNameThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("firstNameEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("lastNameThFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("lastNameEnFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfBirthFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfIssueFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("dateOfExpiryFlag")),
            mapFromStringToBoolean(ocrPrefill.getString("laserIdFlag"))
        );

        ekycUtilities.initEkyc(
                getReactApplicationContext().getApplicationContext(),
                mReactContext,
                this.sessionId,
                this.token,
                BuildConfig.FLAVOR.toUpperCase(),
                customizeTheme,
                null,
                null,
                (success, description, ekycToken) -> {
                    this.ekycToken = ekycToken;
                    if (success) {
                        ekycUtilities.ocrIdCard(
                                mReactContext.getCurrentActivity(),
                                checkExpiredIdCard,
                                checkDopa,
                                enableConfirmInfo,
                                (isPrefillSet)? prefill : null,
                                (ocrSuccess, ocrDescription, userOcrValue, userConfirmedValue, dopaResult) -> {
                                    saveOcrResultString(ocrSuccess, ocrDescription, userOcrValue, userConfirmedValue, dopaResult);
                                }
                        );
                    } else {
                        saveOtherResultString(success, description);
                    }
                }
        );
    }

    private void saveOcrResultString(Boolean ocrSuccess,
                                     String ocrDescription,
                                     UserConfirmedValue userOcrValue,
                                     UserConfirmedValue userConfirmedValue,
                                     DopaResult dopaResult) {
        String dopaCode = "null";
        String dopaDescription = "null";
        this.resultString = "success: " + (ocrSuccess ? "true" : "false") + "\n" +
                "description: " + ocrDescription + "\n";
        
        setFieldForUserOcrValue("userOcrValue", userOcrValue);
        setFieldForUserOcrValue("userConfirmedValue", userConfirmedValue);
        
        if (dopaResult != null) {
            dopaCode = dopaResult.component1();
            dopaDescription = dopaResult.component2();
        }
        this.resultString = this.resultString +
            "dopaCode: " + dopaCode + "\n" +
            "dopaDescription: " + dopaDescription + "\n";
        resultString = resultString + 
            "EKYC Token: " + this.ekycToken +
            "\nInstallationId: " + ekycUtilities.getInstallationId();
    }

    private void setFieldForUserOcrValue(String header, UserConfirmedValue userOcrValue) {
        if (userOcrValue != null) {
            String nationalId = userOcrValue.component1();
            String titleTH = userOcrValue.component2();
            String firstNameTH = userOcrValue.component3();
            String middleTH = userOcrValue.component4();
            String lastnameTH = userOcrValue.component5();
            String titleEN = userOcrValue.component6();
            String firstNameEN = userOcrValue.component7();
            String middleEn = userOcrValue.component8();
            String lastnameEn = userOcrValue.component9();
            String dateOfBirth = userOcrValue.component10();
            String issuedDate = userOcrValue.component11();
            String expDate = userOcrValue.component12();
            String laserId = userOcrValue.component13();
            resultString = resultString + header + ":\n" +
                    "    nationalId: " + nationalId + "\n" +
                    "    titleTH: " + titleTH + "\n" +
                    "    firstNameTH: " + firstNameTH + "\n" +
                    "    middleTH: " + middleTH + "\n" +
                    "    lastnameTH: " + lastnameTH + "\n" +
                    "    titleEN: " + titleEN + "\n" +
                    "    firstNameEN: " + firstNameEN + "\n" +
                    "    middleEn: " + middleEn + "\n" +
                    "    lastnameEn: " + lastnameEn + "\n" +
                    "    dateOfBirth: " + dateOfBirth + "\n" +
                    "    issuedDate: " + issuedDate + "\n" +
                    "    expDate: " + expDate + "\n" +
                    "    laserId: " + laserId + "\n";
        }
        else {
            resultString = resultString + header + ": null\n";
        }
    }

    private void saveNdidResultString(Boolean ndidSuccess,
                                      String ndidDescription,
                                      String ndidStatus,
                                      EkycUtilities.NdidError ndidError,
                                      EkycUtilities.NdidData ndidData) {
        String ndidErrorCode = "null";
        String referenceId = "null";
        String requestId = "null";
        if (ndidError != null) {
            ndidErrorCode = ndidError.component1();
        }
        if (ndidData != null) {
            referenceId = ndidData.component1();
            requestId = ndidData.component2();
        }
        this.resultString = "success: " + (ndidSuccess ? "true" : "false") +
                "\ndescription: " + ndidDescription +
                "\nstatus: " + ndidStatus +
                "\nndidError: " + ndidErrorCode +
                "\nreferenceId: " + referenceId +
                "\nrequestId: " + requestId +
                "\nEKYC Token: " + this.ekycToken +
                "\nInstallationId: " + ekycUtilities.getInstallationId();
    }

    private void saveOtherResultString(Boolean success, String description) {
        resultString = "success: " + (success ? "true" : "false") +
                "\ndescription: " + description +
                "\nEKYC Token: " + this.ekycToken +
                "\nInstallationId: " + ekycUtilities.getInstallationId();
    }

    private Boolean mapFromStringToBoolean(String data) {
        Boolean convertedData = false;
        switch(data) {
            case "true":
                convertedData = true;
                break;
            case "false":
                convertedData = false;
                break;
            default:
                convertedData = null;
        }
        return convertedData;
    }
}
