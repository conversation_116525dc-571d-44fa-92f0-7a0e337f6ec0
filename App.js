import { createAppContainer } from 'react-navigation';
import { createStackNavigator } from "react-navigation-stack";
import Home from './Home';
import Result from './Result';
import { LogBox } from 'react-native';

LogBox.ignoreLogs([
  "[react-native-gesture-handler] Seems like you\'re using an old API with gesture components, check out new Gestures system!",
]);

const app = createStackNavigator({
  Home: {
    screen: Home, navigationOptions: {
      headerShown: false,
    },
  },
  Result: { screen: Result }
});
export default createAppContainer(app);