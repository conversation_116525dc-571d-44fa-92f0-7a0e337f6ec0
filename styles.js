import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fffff',
        alignItems: 'center',
        padding: 10
    },
    image: {
        flex: 1,
        justifyContent: "center"
    },
    transparentBlackBackground: {
        flex: 1,
        backgroundColor: '#000000aa',
        alignItems: 'center',
        padding: 10,
        justifyContent: "center"
    },
    whiteBackground: {
        width: "70%",
        backgroundColor: '#ffffff',
        alignItems: 'center',
        paddingHorizontal:20,
        paddingVertical:20,
        justifyContent: "center"
    },
    row: {
        flexDirection: "row",
    },
    smallButtonLeftView: {
        flex: 1,
        marginLeft: 5,
        marginRight: 2,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        alignItems: "center"
    },
    smallButtonRightView: {
        flex: 1,
        marginLeft: 2,
        marginRight: 5,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10,
        alignItems: "center"
    },
    largeButtonView: {
        flex: 1,
        borderRadius: 10,
        alignItems: "center"
    },
    smallButtonText: {
        color: "#FFFFFF",
        marginHorizontal: 6,
        marginVertical: 10,
        fontSize: 14,
        fontWeight: 'bold'
    },
    input: {
        height: 40,
        width: "90%",
        margin: 10,
        borderWidth: 1,
        padding: 10,
      },
    top: {
        flex: 1, 
        alignItems: "center",
        justifyContent: "center"
    },
    center: {
        flex: 1, 
        width: "100%", 
        alignItems: "center", 
        justifyContent: "center"
    },
    bottom: {
        flex: 1, 
        width: "100%", 
        alignItems: "center", 
        justifyContent: "flex-end",
    },
    normalView: {
        width: "90%", 
        margin: 10, 
        borderColor: "#FFFFFF", 
        borderWidth: 1, 
        borderRadius: 10,
        alignItems: "center"
    },
    disableButtonView: {
        width: "90%", 
        margin: 10, 
        borderColor: "#FFFFFF50",
        backgroundColor: "FFFFFF50", 
        borderWidth: 1, 
        borderRadius: 10,
        alignItems: "center"
    },
    normalViewText: {
        color: "#FFFFFF",
        margin: 10,
        fontSize: 18,
        fontWeight: 'bold'
    },
    disableViewText: {
        color: "#FFFFFF50",
        margin: 10,
        fontSize: 18,
        fontWeight: 'bold'
    },
    outlineView: {
        width: "90%", 
        margin: 10, 
        borderColor: "#FFFFFF", 
        borderWidth: 1, 
        borderRadius: 10,
        alignItems: "center"
    },
    disableView: {
        width: "90%", 
        margin: 10, 
        backgroundColor: "#b2bec3", 
        borderRadius: 10,
        alignItems: "center"
    },
    outlineViewText: {
        color: "#FFFFFF",
        margin: 10,
        fontSize: 18,
        fontWeight: 'bold'
    },
    techxLogo: {
        width: 90,
        height: 90,
    },
    techxLabel: {
        marginTop: 16,
        marginBottom: 16,
        width: 95,
        height: 20,
    },
    scrollPopup: {
        height: 200,
        width: '90%'
    },
    space: {
        height: 10
    }
});

export default styles;