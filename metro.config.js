/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
    // Fix for React Navigation v4 asset issues
    assetRegistryPath: 'react-native/Libraries/Image/AssetRegistry',
  },
  resolver: {
    assetExts: [...defaultConfig.resolver.assetExts, 'bin', 'txt', 'jpg', 'png', 'json', 'gif', 'webp', 'svg'],
    sourceExts: [...defaultConfig.resolver.sourceExts, 'js', 'jsx', 'ts', 'tsx'],
    // Allow resolving assets from node_modules
    platforms: ['ios', 'android', 'native', 'web'],
  },
  // Additional configuration for asset handling
  watchFolders: [],
  maxWorkers: 2,
};

module.exports = mergeConfig(defaultConfig, config);
